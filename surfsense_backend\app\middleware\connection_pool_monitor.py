"""
Connection Pool Monitoring Middleware

This middleware monitors database connection pool status and logs warnings
when connection usage is high or when potential leaks are detected.
"""
import logging
import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.db import engine

logger = logging.getLogger(__name__)

class ConnectionPoolMonitorMiddleware(BaseHTTPMiddleware):
    """
    Middleware to monitor database connection pool status.
    
    This middleware:
    1. Logs connection pool status before and after each request
    2. Warns when connection usage is high
    3. Detects potential connection leaks
    4. Provides metrics for monitoring
    """
    
    def __init__(self, app, log_threshold: float = 0.8, log_every_n_requests: int = 10):
        super().__init__(app)
        self.log_threshold = log_threshold  # Log when pool usage exceeds this ratio
        self.log_every_n_requests = log_every_n_requests
        self.request_count = 0
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and monitor connection pool."""
        
        self.request_count += 1
        start_time = time.time()
        
        # Get initial pool status
        initial_pool_status = self._get_pool_status()
        
        # Log pool status periodically or when usage is high
        should_log = (
            self.request_count % self.log_every_n_requests == 0 or
            initial_pool_status['usage_ratio'] > self.log_threshold
        )
        
        if should_log:
            logger.info(f"Connection pool status before request {self.request_count}: {initial_pool_status}")
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Get final pool status
            final_pool_status = self._get_pool_status()
            
            # Check for potential leaks (connections not returned after request)
            connection_diff = final_pool_status['checked_out'] - initial_pool_status['checked_out']
            
            if connection_diff > 0:
                logger.warning(
                    f"Potential connection leak detected! "
                    f"Connections increased by {connection_diff} during request. "
                    f"Path: {request.url.path}, Method: {request.method}"
                )
            
            # Log if usage is still high after request
            if should_log or final_pool_status['usage_ratio'] > self.log_threshold:
                duration = time.time() - start_time
                logger.info(
                    f"Connection pool status after request {self.request_count} "
                    f"(duration: {duration:.2f}s): {final_pool_status}"
                )
            
            return response
            
        except Exception as e:
            # Log pool status on error
            error_pool_status = self._get_pool_status()
            logger.error(
                f"Error during request {self.request_count}. "
                f"Pool status: {error_pool_status}. "
                f"Error: {str(e)}"
            )
            raise
    
    def _get_pool_status(self) -> dict:
        """Get current connection pool status."""
        try:
            pool = engine.pool
            
            # Get pool statistics
            size = pool.size()
            checked_in = pool.checkedin()
            checked_out = pool.checkedout()
            overflow = pool.overflow()
            invalid = pool.invalid()
            
            # Calculate usage ratio
            total_capacity = size + overflow
            usage_ratio = checked_out / total_capacity if total_capacity > 0 else 0
            
            return {
                'size': size,
                'checked_in': checked_in,
                'checked_out': checked_out,
                'overflow': overflow,
                'invalid': invalid,
                'total_capacity': total_capacity,
                'usage_ratio': usage_ratio,
                'available': checked_in
            }
            
        except Exception as e:
            logger.error(f"Error getting pool status: {str(e)}")
            return {
                'error': str(e),
                'size': 0,
                'checked_in': 0,
                'checked_out': 0,
                'overflow': 0,
                'invalid': 0,
                'total_capacity': 0,
                'usage_ratio': 0,
                'available': 0
            }


def log_pool_status(context: str = ""):
    """
    Utility function to log current pool status.
    
    Args:
        context: Additional context for the log message
    """
    try:
        pool = engine.pool
        
        size = pool.size()
        checked_in = pool.checkedin()
        checked_out = pool.checkedout()
        overflow = pool.overflow()
        invalid = pool.invalid()
        
        total_capacity = size + overflow
        usage_ratio = checked_out / total_capacity if total_capacity > 0 else 0
        
        status = {
            'context': context,
            'size': size,
            'checked_in': checked_in,
            'checked_out': checked_out,
            'overflow': overflow,
            'invalid': invalid,
            'total_capacity': total_capacity,
            'usage_ratio': usage_ratio,
            'available': checked_in
        }
        
        if usage_ratio > 0.8:
            logger.warning(f"High connection pool usage: {status}")
        else:
            logger.info(f"Connection pool status: {status}")
            
    except Exception as e:
        logger.error(f"Error logging pool status: {str(e)}")


async def check_for_connection_leaks():
    """
    Periodic task to check for connection leaks.
    This can be called from a background task or cron job.
    """
    try:
        pool = engine.pool
        checked_out = pool.checkedout()
        
        if checked_out > 0:
            logger.warning(
                f"Potential connection leak detected: {checked_out} connections "
                f"are still checked out during maintenance check"
            )
            
            # Log detailed pool status
            log_pool_status("leak_check")
            
    except Exception as e:
        logger.error(f"Error checking for connection leaks: {str(e)}")
