from typing import List, Dict, <PERSON>, <PERSON><PERSON>, NamedTuple
from langchain_core.messages import BaseMessage
from litellm import token_counter, get_model_info


class DocumentTokenInfo(NamedTuple):
    """Information about a document and its token cost."""
    index: int
    document: Dict[str, Any]
    formatted_content: str
    token_count: int


def convert_langchain_messages_to_dict(messages: List[BaseMessage]) -> List[Dict[str, str]]:
    """Convert LangChain messages to format expected by token_counter."""
    role_mapping = {
        'system': 'system',
        'human': 'user',
        'ai': 'assistant'
    }

    converted_messages = []
    for msg in messages:
        role = role_mapping.get(getattr(msg, 'type', None), 'user')
        converted_messages.append({
            "role": role,
            "content": str(msg.content)
        })

    return converted_messages


def format_document_for_citation(document: Dict[str, Any], priority_index: int = None) -> str:
    """Format a single document for citation in the standard XML format."""
    content = document.get("content", "")
    doc_info = document.get("document", {})
    document_id = doc_info.get("id", "")
    document_type = doc_info.get("document_type", "CRAWLED_URL")
    score = document.get("score", 0.0)

    # Add priority information to help LLM understand document importance
    priority_info = ""
    if priority_index is not None:
        priority_info = f"""
        <priority_rank>{priority_index}</priority_rank>
        <relevance_score>{score:.4f}</relevance_score>"""

    return f"""<document>
    <metadata>
        <source_id>{document_id}</source_id>
        <source_type>{document_type}</source_type>{priority_info}
    </metadata>
    <content>
        {content}
    </content>
    </document>"""


def format_documents_section(documents: List[Dict[str, Any]], section_title: str = "Source material") -> str:
    """
    Format multiple documents into a complete documents section.

    Documents are presented in reverse order (highest priority last) to leverage
    LLM's recency bias - the tendency to pay more attention to information that
    appears later in the context.
    """
    if not documents:
        return ""

    # Reverse the order so highest priority documents appear last (closest to the question)
    # This helps LLM pay more attention to the most relevant documents
    reversed_docs = list(reversed(documents))

    # Format each document with priority information
    formatted_docs = []
    for i, doc in enumerate(reversed_docs):
        # Priority rank: 1 = highest priority (was last in original list)
        priority_rank = len(documents) - i
        formatted_doc = format_document_for_citation(doc, priority_rank)
        formatted_docs.append(formatted_doc)

    return f"""{section_title} (ordered by relevance, highest priority documents appear last):
    <documents>
    {chr(10).join(formatted_docs)}
    </documents>"""


def calculate_document_token_costs(documents: List[Dict[str, Any]], model: str) -> List[DocumentTokenInfo]:
    """Pre-calculate token costs for each document."""
    document_token_info = []

    for i, doc in enumerate(documents):
        # Format document with priority information for accurate token counting
        priority_rank = i + 1  # Temporary rank for token calculation
        formatted_doc = format_document_for_citation(doc, priority_rank)

        # Calculate token count for this document
        token_count = token_counter(
            messages=[{"role": "user", "content": formatted_doc}],
            model=model
        )

        document_token_info.append(DocumentTokenInfo(
            index=i,
            document=doc,
            formatted_content=formatted_doc,
            token_count=token_count
        ))

    return document_token_info


def find_optimal_documents_with_binary_search(
    document_tokens: List[DocumentTokenInfo],
    available_tokens: int
) -> List[DocumentTokenInfo]:
    """
    Use binary search to find the maximum number of documents that fit within token limit.

    IMPORTANT: This function assumes documents are already sorted by priority (highest score first).
    It will select documents from the beginning of the list, so the input should be pre-sorted
    by relevance/score in descending order.
    """
    if not document_tokens or available_tokens <= 0:
        return []

    left, right = 0, len(document_tokens)
    optimal_docs = []

    while left <= right:
        mid = (left + right) // 2
        # Take the first 'mid' documents (highest priority ones)
        current_docs = document_tokens[:mid]
        current_token_sum = sum(
            doc_info.token_count for doc_info in current_docs)

        if current_token_sum <= available_tokens:
            optimal_docs = current_docs
            left = mid + 1
        else:
            right = mid - 1

    return optimal_docs


def get_model_context_window(model_name: str) -> int:
    """Get the total context window size for a model (input + output tokens)."""
    try:
        model_info = get_model_info(model_name)
        context_window = model_info.get(
            'max_input_tokens', 4096)  # Default fallback
        return context_window
    except Exception as e:
        print(
            f"Warning: Could not get model info for {model_name}, using default 4096 tokens. Error: {e}")
        return 4096  # Conservative fallback


def optimize_documents_for_token_limit(
    documents: List[Dict[str, Any]],
    base_messages: List[BaseMessage],
    model_name: str
) -> Tuple[List[Dict[str, Any]], bool]:
    """
    Optimize documents to fit within token limits using binary search.

    This function prioritizes documents with higher scores/relevance by sorting them
    before applying token limit optimization.

    Args:
        documents: List of documents with content and metadata (should have 'score' field)
        base_messages: Base messages without documents (chat history + system + human message template)
        model_name: Model name for token counting (required)

    Returns:
        Tuple of (optimized_documents, has_documents_remaining)
    """
    if not documents:
        return [], False

    # Sort documents by score in descending order to prioritize high-relevance documents
    # This ensures that when we apply token limits, we keep the most relevant documents
    sorted_documents = sorted(documents, key=lambda x: x.get("score", 0), reverse=True)

    print(f"Document prioritization: Sorted {len(sorted_documents)} documents by score")
    if sorted_documents:
        highest_score = sorted_documents[0].get("score", 0)
        lowest_score = sorted_documents[-1].get("score", 0)
        print(f"Score range: {highest_score:.4f} (highest) to {lowest_score:.4f} (lowest)")

    model = model_name
    context_window = get_model_context_window(model)

    # Calculate base token cost
    base_messages_dict = convert_langchain_messages_to_dict(base_messages)
    base_tokens = token_counter(messages=base_messages_dict, model=model)
    available_tokens_for_docs = context_window - base_tokens

    print(
        f"Token optimization: Context window={context_window}, Base={base_tokens}, Available for docs={available_tokens_for_docs}")

    if available_tokens_for_docs <= 0:
        print("No tokens available for documents after base content and output buffer")
        return [], False

    # Calculate token costs for all documents (now sorted by priority)
    document_token_info = calculate_document_token_costs(sorted_documents, model)

    # Find optimal number of documents using binary search
    # This will select the highest-scoring documents that fit within token limits
    optimal_doc_info = find_optimal_documents_with_binary_search(
        document_token_info,
        available_tokens_for_docs
    )

    # Extract the original document objects (maintaining priority order)
    optimized_documents = [doc_info.document for doc_info in optimal_doc_info]
    has_documents_remaining = len(optimized_documents) > 0

    print(
        f"Token optimization result: Using {len(optimized_documents)}/{len(documents)} documents")

    if optimized_documents:
        selected_scores = [doc.get("score", 0) for doc in optimized_documents]
        print(f"Selected documents score range: {max(selected_scores):.4f} to {min(selected_scores):.4f}")

    return optimized_documents, has_documents_remaining


def calculate_token_count(messages: List[BaseMessage], model_name: str) -> int:
    """Calculate token count for a list of LangChain messages."""
    model = model_name
    messages_dict = convert_langchain_messages_to_dict(messages)
    return token_counter(messages=messages_dict, model=model)
