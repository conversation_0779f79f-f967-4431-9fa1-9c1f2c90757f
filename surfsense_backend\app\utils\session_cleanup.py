"""
Session cleanup utilities for preventing connection pool leaks.

This module provides utilities to:
1. Monitor active sessions
2. Force cleanup of stale sessions
3. Provide session lifecycle management
"""
import asyncio
import logging
import time
import weakref
from typing import Dict, Set, Optional
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.db import async_session_maker, engine

logger = logging.getLogger(__name__)

class SessionTracker:
    """
    Tracks active sessions to detect and cleanup potential leaks.
    """
    
    def __init__(self):
        self._active_sessions: Dict[int, Dict] = {}
        self._session_refs: Set[weakref.ref] = set()
        self._lock = asyncio.Lock()
        
    async def register_session(self, session: AsyncSession, context: str = "unknown") -> int:
        """Register a new session for tracking."""
        session_id = id(session)
        
        async with self._lock:
            self._active_sessions[session_id] = {
                'session': session,
                'context': context,
                'created_at': time.time(),
                'last_activity': time.time()
            }
            
            # Create weak reference to detect when session is garbage collected
            def cleanup_callback(ref):
                asyncio.create_task(self._cleanup_session_ref(session_id, ref))
            
            session_ref = weakref.ref(session, cleanup_callback)
            self._session_refs.add(session_ref)
            
        logger.debug(f"Registered session {session_id} for context: {context}")
        return session_id
        
    async def update_activity(self, session_id: int):
        """Update last activity time for a session."""
        async with self._lock:
            if session_id in self._active_sessions:
                self._active_sessions[session_id]['last_activity'] = time.time()
                
    async def unregister_session(self, session_id: int):
        """Unregister a session when it's properly closed."""
        async with self._lock:
            if session_id in self._active_sessions:
                context = self._active_sessions[session_id]['context']
                del self._active_sessions[session_id]
                logger.debug(f"Unregistered session {session_id} for context: {context}")
                
    async def _cleanup_session_ref(self, session_id: int, ref: weakref.ref):
        """Cleanup session reference when session is garbage collected."""
        async with self._lock:
            self._session_refs.discard(ref)
            if session_id in self._active_sessions:
                context = self._active_sessions[session_id]['context']
                del self._active_sessions[session_id]
                logger.warning(f"Session {session_id} was garbage collected without proper cleanup. Context: {context}")
                
    async def get_stale_sessions(self, max_age_seconds: int = 1800) -> Dict[int, Dict]:
        """Get sessions that have been active for too long."""
        current_time = time.time()
        stale_sessions = {}
        
        async with self._lock:
            for session_id, info in self._active_sessions.items():
                age = current_time - info['created_at']
                if age > max_age_seconds:
                    stale_sessions[session_id] = {
                        **info,
                        'age_seconds': age
                    }
                    
        return stale_sessions
        
    async def force_cleanup_stale_sessions(self, max_age_seconds: int = 1800) -> int:
        """Force cleanup of stale sessions."""
        stale_sessions = await self.get_stale_sessions(max_age_seconds)
        cleanup_count = 0
        
        for session_id, info in stale_sessions.items():
            try:
                session = info['session']
                if session and not session.is_active:
                    await session.close()
                    await self.unregister_session(session_id)
                    cleanup_count += 1
                    logger.warning(
                        f"Force closed stale session {session_id} "
                        f"(age: {info['age_seconds']:.1f}s, context: {info['context']})"
                    )
            except Exception as e:
                logger.error(f"Error force closing session {session_id}: {str(e)}")
                
        return cleanup_count
        
    async def get_status(self) -> Dict:
        """Get current session tracking status."""
        async with self._lock:
            return {
                'active_sessions': len(self._active_sessions),
                'session_refs': len(self._session_refs),
                'sessions_by_context': {
                    info['context']: 1 
                    for info in self._active_sessions.values()
                }
            }

# Global session tracker instance
session_tracker = SessionTracker()

@asynccontextmanager
async def tracked_session(context: str = "unknown"):
    """
    Context manager that creates a tracked session with automatic cleanup.
    
    Usage:
        async with tracked_session("my_operation") as session:
            # Use session here
            result = await session.execute(query)
    """
    session = None
    session_id = None
    
    try:
        session = async_session_maker()
        session_id = await session_tracker.register_session(session, context)
        
        yield session
        
        # Commit if no exceptions occurred
        await session.commit()
        
    except Exception as e:
        # Rollback on error
        if session:
            try:
                await session.rollback()
            except Exception as rollback_error:
                logger.error(f"Error during rollback for session {session_id}: {str(rollback_error)}")
        raise
        
    finally:
        # Always close and unregister session
        if session:
            try:
                await session.close()
            except Exception as close_error:
                logger.error(f"Error closing session {session_id}: {str(close_error)}")
                
        if session_id:
            await session_tracker.unregister_session(session_id)

async def cleanup_background_task():
    """
    Background task to periodically cleanup stale sessions.
    This should be run as a background task in your application.
    """
    while True:
        try:
            # Check for stale sessions every 5 minutes
            await asyncio.sleep(300)
            
            # Force cleanup sessions older than 30 minutes
            cleanup_count = await session_tracker.force_cleanup_stale_sessions(1800)
            
            if cleanup_count > 0:
                logger.warning(f"Cleaned up {cleanup_count} stale sessions")
                
            # Log current status
            status = await session_tracker.get_status()
            logger.info(f"Session tracker status: {status}")
            
            # Log connection pool status
            try:
                pool = engine.pool
                pool_status = {
                    'size': pool.size(),
                    'checked_in': pool.checkedin(),
                    'checked_out': pool.checkedout(),
                    'overflow': pool.overflow(),
                    'invalid': pool.invalid()
                }
                logger.info(f"Connection pool status: {pool_status}")
                
                # Warn if pool usage is high
                total_capacity = pool.size() + pool.overflow()
                if total_capacity > 0:
                    usage_ratio = pool.checkedout() / total_capacity
                    if usage_ratio > 0.8:
                        logger.warning(f"High connection pool usage: {usage_ratio:.2%}")
                        
            except Exception as e:
                logger.error(f"Error getting pool status: {str(e)}")
                
        except Exception as e:
            logger.error(f"Error in session cleanup background task: {str(e)}")
            # Continue running even if there's an error
            await asyncio.sleep(60)  # Wait 1 minute before retrying
