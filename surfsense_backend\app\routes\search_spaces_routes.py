from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List
from app.db import get_async_session, User, SearchSpace, SearchSpaceUser
from app.schemas import SearchSpaceCreate, SearchSpaceUpdate, SearchSpaceRead
from app.users import current_active_user
from app.utils.check_ownership import check_ownership
from fastapi import HTTPException

router = APIRouter()

@router.post("/searchspaces/", response_model=SearchSpaceRead)
async def create_search_space(
    search_space: SearchSpaceCreate,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    try:
        db_search_space = SearchSpace(**search_space.model_dump(), user_id=user.id)
        session.add(db_search_space)
        await session.commit()
        await session.refresh(db_search_space)
        return db_search_space
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create search space: {str(e)}"
        )

@router.get("/searchspaces/", response_model=List[SearchSpaceRead])
async def read_search_spaces(
    skip: int = 0,
    limit: int = 200,
    include_shared: bool = True,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """
    Get SearchSpaces for the current user.
    Includes both owned and shared SearchSpaces by default.
    """
    try:
        # Get owned SearchSpaces
        owned_result = await session.execute(
            select(SearchSpace)
            .filter(SearchSpace.user_id == user.id)
            .offset(skip)
            .limit(limit)
        )
        owned_spaces = owned_result.scalars().all()

        if not include_shared:
            return owned_spaces

        # Get shared SearchSpaces
        shared_result = await session.execute(
            select(SearchSpace)
            .join(SearchSpaceUser, SearchSpace.id == SearchSpaceUser.search_space_id)
            .filter(SearchSpaceUser.user_id == user.id)
            .offset(skip)
            .limit(limit)
        )
        shared_spaces = shared_result.scalars().all()

        # Combine and deduplicate (in case user owns and is shared the same space)
        all_spaces = owned_spaces + [space for space in shared_spaces if space not in owned_spaces]

        return all_spaces[:limit]  # Respect the limit

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch search spaces: {str(e)}"
        )

@router.get("/searchspaces/{search_space_id}", response_model=SearchSpaceRead)
async def read_search_space(
    search_space_id: int,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    try:
        search_space = await check_ownership(session, SearchSpace, search_space_id, user)
        return search_space
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch search space: {str(e)}"
        )

@router.put("/searchspaces/{search_space_id}", response_model=SearchSpaceRead)
async def update_search_space(
    search_space_id: int,
    search_space_update: SearchSpaceUpdate,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    try:
        db_search_space = await check_ownership(session, SearchSpace, search_space_id, user)
        update_data = search_space_update.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_search_space, key, value)
        await session.commit()
        await session.refresh(db_search_space)
        return db_search_space
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update search space: {str(e)}"
        )

@router.delete("/searchspaces/{search_space_id}", response_model=dict)
async def delete_search_space(
    search_space_id: int,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    try:
        db_search_space = await check_ownership(session, SearchSpace, search_space_id, user)
        await session.delete(db_search_space)
        await session.commit()
        return {"message": "Search space deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete search space: {str(e)}"
        ) 