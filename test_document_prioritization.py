#!/usr/bin/env python3
"""
Test script to verify document prioritization fix.

This script tests that documents with higher scores are prioritized
over documents with lower scores during token optimization.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'surfsense_backend'))

from app.agents.researcher.utils import optimize_documents_for_token_limit
from langchain_core.messages import HumanMessage, SystemMessage

def create_test_documents():
    """Create test documents with different scores."""
    documents = [
        {
            "chunk_id": "doc_1",
            "content": "This is document 1 with high relevance score. " * 20,  # Make it longer
            "score": 0.95,
            "document": {
                "id": "1",
                "title": "High Score Document",
                "document_type": "TEST",
                "metadata": {}
            }
        },
        {
            "chunk_id": "doc_2", 
            "content": "This is document 2 with medium relevance score. " * 20,
            "score": 0.75,
            "document": {
                "id": "2",
                "title": "Medium Score Document",
                "document_type": "TEST",
                "metadata": {}
            }
        },
        {
            "chunk_id": "doc_3",
            "content": "This is document 3 with low relevance score. " * 20,
            "score": 0.45,
            "document": {
                "id": "3", 
                "title": "Low Score Document",
                "document_type": "TEST",
                "metadata": {}
            }
        },
        {
            "chunk_id": "doc_4",
            "content": "This is document 4 with very low relevance score. " * 20,
            "score": 0.25,
            "document": {
                "id": "4",
                "title": "Very Low Score Document", 
                "document_type": "TEST",
                "metadata": {}
            }
        },
        {
            "chunk_id": "doc_5",
            "content": "This is document 5 with highest relevance score. " * 20,
            "score": 0.98,
            "document": {
                "id": "5",
                "title": "Highest Score Document",
                "document_type": "TEST", 
                "metadata": {}
            }
        }
    ]
    return documents

def create_base_messages():
    """Create base messages for token calculation."""
    return [
        SystemMessage(content="You are a helpful assistant."),
        HumanMessage(content="Please answer the following question using the provided documents.")
    ]

def test_document_prioritization():
    """Test that documents are prioritized by score."""
    print("=== Testing Document Prioritization ===")
    
    # Create test data
    documents = create_test_documents()
    base_messages = create_base_messages()
    
    print(f"\nOriginal documents order:")
    for i, doc in enumerate(documents):
        print(f"  {i+1}. {doc['document']['title']} (score: {doc['score']})")
    
    # Test with a model that has limited context
    model_name = "gpt-3.5-turbo"  # Use a model with smaller context window
    
    try:
        optimized_docs, has_docs = optimize_documents_for_token_limit(
            documents, base_messages, model_name
        )
        
        print(f"\nOptimized documents (selected {len(optimized_docs)}/{len(documents)}):")
        if optimized_docs:
            for i, doc in enumerate(optimized_docs):
                print(f"  {i+1}. {doc['document']['title']} (score: {doc['score']})")
            
            # Verify that documents are sorted by score
            scores = [doc['score'] for doc in optimized_docs]
            is_sorted_desc = all(scores[i] >= scores[i+1] for i in range(len(scores)-1))
            
            print(f"\nVerification:")
            print(f"  Documents sorted by score (desc): {'✓' if is_sorted_desc else '✗'}")
            print(f"  Highest score selected: {max(scores):.3f}")
            print(f"  Lowest score selected: {min(scores):.3f}")
            
            # Check if highest scoring documents were selected
            original_scores = sorted([doc['score'] for doc in documents], reverse=True)
            selected_scores = sorted(scores, reverse=True)
            
            print(f"  Top scores available: {original_scores[:len(selected_scores)]}")
            print(f"  Top scores selected: {selected_scores}")
            
            matches_top_scores = selected_scores == original_scores[:len(selected_scores)]
            print(f"  Selected top-scoring documents: {'✓' if matches_top_scores else '✗'}")
            
            if matches_top_scores:
                print("\n🎉 SUCCESS: Document prioritization is working correctly!")
                print("   Higher-scored documents are being prioritized over lower-scored ones.")
            else:
                print("\n❌ FAILURE: Document prioritization is not working correctly!")
                print("   Lower-scored documents may be selected over higher-scored ones.")
                
        else:
            print("  No documents were selected (token limit too restrictive)")
            
    except Exception as e:
        print(f"\n❌ ERROR during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_document_prioritization()
