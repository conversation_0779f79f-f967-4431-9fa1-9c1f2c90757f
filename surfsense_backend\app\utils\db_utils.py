"""
Database utilities for proper session management and connection handling.
"""
import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional, Any, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.db import async_session_maker

logger = logging.getLogger(__name__)


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Context manager for database sessions that ensures proper cleanup.
    
    This context manager:
    1. Creates a new session
    2. Yields it for use
    3. Commits if no exceptions occur
    4. Rolls back if exceptions occur
    5. Always closes the session
    
    Usage:
        async with get_db_session() as session:
            # Use session here
            result = await session.execute(query)
            # Session will be automatically committed and closed
    """
    session = None
    try:
        session = async_session_maker()
        yield session
        await session.commit()
    except Exception as e:
        if session:
            await session.rollback()
        logger.error(f"Database session error: {str(e)}")
        raise
    finally:
        if session:
            await session.close()


async def execute_with_retry(
    operation: Callable[[AsyncSession], Any],
    max_retries: int = 3,
    retry_delay: float = 1.0
) -> Any:
    """
    Execute a database operation with retry logic.
    
    Args:
        operation: Async function that takes a session and returns a result
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds
        
    Returns:
        Result of the operation
        
    Raises:
        SQLAlchemyError: If all retries fail
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            async with get_db_session() as session:
                return await operation(session)
        except SQLAlchemyError as e:
            last_exception = e
            if attempt < max_retries:
                logger.warning(f"Database operation failed (attempt {attempt + 1}/{max_retries + 1}): {str(e)}")
                await asyncio.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
            else:
                logger.error(f"Database operation failed after {max_retries + 1} attempts: {str(e)}")
                
    raise last_exception


async def safe_background_task(
    task_func: Callable[[AsyncSession], Any],
    task_name: str = "background_task"
) -> Optional[Any]:
    """
    Safely execute a background task with proper session management and error handling.
    
    Args:
        task_func: Async function that takes a session and performs the task
        task_name: Name of the task for logging purposes
        
    Returns:
        Result of the task or None if it failed
    """
    try:
        logger.info(f"Starting background task: {task_name}")
        
        async with get_db_session() as session:
            result = await task_func(session)
            logger.info(f"Background task completed successfully: {task_name}")
            return result
            
    except Exception as e:
        logger.error(f"Background task failed: {task_name} - {str(e)}")
        return None


class SessionManager:
    """
    Session manager for long-running operations that need to manage multiple sessions.
    """
    
    def __init__(self):
        self._sessions = {}
        self._lock = asyncio.Lock()
    
    async def get_session(self, key: str) -> AsyncSession:
        """Get or create a session for the given key."""
        async with self._lock:
            if key not in self._sessions:
                self._sessions[key] = async_session_maker()
            return self._sessions[key]
    
    async def close_session(self, key: str):
        """Close and remove a session."""
        async with self._lock:
            if key in self._sessions:
                session = self._sessions.pop(key)
                try:
                    await session.close()
                except Exception as e:
                    logger.error(f"Error closing session {key}: {str(e)}")
    
    async def close_all_sessions(self):
        """Close all managed sessions."""
        async with self._lock:
            for key, session in self._sessions.items():
                try:
                    await session.close()
                except Exception as e:
                    logger.error(f"Error closing session {key}: {str(e)}")
            self._sessions.clear()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close_all_sessions()
