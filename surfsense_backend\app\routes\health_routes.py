"""
Health check routes for monitoring application and database status.
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from typing import Dict, Any
import logging
import asyncio

from app.db import get_async_session, engine
from app.middleware.db_monitoring import log_pool_status
from app.users import current_active_user
from app.db import User

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/health")
async def health_check():
    """Basic health check endpoint."""
    return {"status": "healthy", "service": "SurfSense Backend"}


@router.get("/health/database")
async def database_health_check(session: AsyncSession = Depends(get_async_session)):
    """Check database connectivity and basic functionality."""
    try:
        # Test basic database connectivity
        result = await session.execute(text("SELECT 1"))
        result.scalar()
        
        return {
            "status": "healthy",
            "database": "connected",
            "message": "Database is accessible"
        }
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Database health check failed: {str(e)}")


@router.get("/health/pool")
async def connection_pool_status(user: User = Depends(current_active_user)):
    """
    Get connection pool status. Requires authentication.
    This endpoint helps monitor for connection leaks.
    """
    try:
        pool_stats = log_pool_status()
        
        if pool_stats is None:
            raise HTTPException(status_code=500, detail="Unable to retrieve pool statistics")
        
        # Calculate pool utilization percentage
        utilization = (pool_stats["checked_out"] / pool_stats["size"]) * 100 if pool_stats["size"] > 0 else 0
        
        # Determine health status based on utilization
        if utilization > 90:
            status = "critical"
            message = "Connection pool is critically full - investigate immediately"
        elif utilization > 70:
            status = "warning"
            message = "Connection pool utilization is high - monitor closely"
        else:
            status = "healthy"
            message = "Connection pool utilization is normal"
        
        return {
            "status": status,
            "message": message,
            "pool_stats": pool_stats,
            "utilization_percent": round(utilization, 1)
        }
        
    except Exception as e:
        logger.error(f"Pool status check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Pool status check failed: {str(e)}")


@router.get("/health/detailed")
async def detailed_health_check(
    user: User = Depends(current_active_user),
    session: AsyncSession = Depends(get_async_session)
):
    """
    Comprehensive health check including database and pool status.
    Requires authentication.
    """
    try:
        health_data = {
            "service": "SurfSense Backend",
            "timestamp": asyncio.get_event_loop().time(),
            "checks": {}
        }
        
        # Database connectivity check
        try:
            result = await session.execute(text("SELECT 1"))
            result.scalar()
            health_data["checks"]["database"] = {
                "status": "healthy",
                "message": "Database is accessible"
            }
        except Exception as e:
            health_data["checks"]["database"] = {
                "status": "unhealthy",
                "message": f"Database check failed: {str(e)}"
            }
        
        # Connection pool check
        try:
            pool_stats = log_pool_status()
            if pool_stats:
                utilization = (pool_stats["checked_out"] / pool_stats["size"]) * 100 if pool_stats["size"] > 0 else 0
                
                if utilization > 90:
                    pool_status = "critical"
                elif utilization > 70:
                    pool_status = "warning"
                else:
                    pool_status = "healthy"
                
                health_data["checks"]["connection_pool"] = {
                    "status": pool_status,
                    "stats": pool_stats,
                    "utilization_percent": round(utilization, 1)
                }
            else:
                health_data["checks"]["connection_pool"] = {
                    "status": "unknown",
                    "message": "Unable to retrieve pool statistics"
                }
        except Exception as e:
            health_data["checks"]["connection_pool"] = {
                "status": "error",
                "message": f"Pool check failed: {str(e)}"
            }
        
        # Overall status determination
        statuses = [check["status"] for check in health_data["checks"].values()]
        if "critical" in statuses or "unhealthy" in statuses:
            health_data["overall_status"] = "unhealthy"
        elif "warning" in statuses:
            health_data["overall_status"] = "warning"
        else:
            health_data["overall_status"] = "healthy"
        
        return health_data
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.post("/health/pool/force-cleanup")
async def force_pool_cleanup(user: User = Depends(current_active_user)):
    """
    Force cleanup of connection pool. Use with caution.
    This endpoint is for emergency situations where connections are stuck.
    """
    try:
        logger.warning(f"Force pool cleanup requested by user {user.id}")
        
        # Get current stats before cleanup
        before_stats = log_pool_status()
        
        # Force dispose of the engine pool
        await engine.dispose()
        
        # Get stats after cleanup
        after_stats = log_pool_status()
        
        return {
            "status": "completed",
            "message": "Connection pool has been forcefully cleaned up",
            "before_cleanup": before_stats,
            "after_cleanup": after_stats
        }
        
    except Exception as e:
        logger.error(f"Force pool cleanup failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Pool cleanup failed: {str(e)}")
