# SQLAlchemy Connection Pool Leak Fixes

## Problem Description

The application was experiencing SQLAlchemy connection pool errors:
```
2025-07-06 18:49:02 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - The garbage collector is trying to clean up non-checked-in connection
```

This error indicates that database connections were not being properly returned to the connection pool, leading to connection exhaustion and potential application instability.

## Root Cause Analysis

The primary issue was identified in the session lifecycle management, particularly:

1. **Streaming Operations**: Long-running streaming operations were receiving sessions from FastAPI dependency injection and holding them for extended periods
2. **Background Tasks**: Background tasks were creating sessions but not always ensuring proper cleanup
3. **Error Handling**: Insufficient error handling in session management leading to unclosed sessions
4. **Connection Pool Configuration**: Suboptimal connection pool settings

## Implemented Solutions

### 1. Enhanced Database Engine Configuration

**File**: `surfsense_backend/app/db.py`

```python
engine = create_async_engine(
    DATABASE_URL,
    pool_pre_ping=True,        # Test connections before use
    pool_recycle=3600,         # Recycle connections after 1 hour
    pool_size=10,              # Base pool size
    max_overflow=20,           # Additional connections when needed
    echo=False
)
```

### 2. Improved Session Management Utilities

**File**: `surfsense_backend/app/utils/db_utils.py`

Created comprehensive utilities for proper session management:

- `get_db_session()`: Context manager ensuring proper session cleanup
- `safe_background_task()`: Wrapper for background tasks with error handling
- `execute_with_retry()`: Retry logic for database operations
- `SessionManager`: For managing multiple sessions in long-running operations

### 3. Fixed Streaming Operations

**File**: `surfsense_backend/app/routes/chats_routes.py`

Created dedicated session management for streaming operations:

```python
async def stream_connector_search_results_with_new_session(...):
    async with get_db_session() as session:
        try:
            async for result in stream_connector_search_results(...):
                yield result
        except Exception as e:
            # Proper error handling and cleanup
            yield error_message
```

### 4. Updated Background Task Functions

**Files**: 
- `surfsense_backend/app/routes/documents_routes.py`
- `surfsense_backend/app/routes/search_source_connectors_routes.py`
- `surfsense_backend/app/routes/podcasts_routes.py`

Replaced manual session creation with `safe_background_task()` wrapper:

```python
# Before
async with async_session_maker() as session:
    await some_task(session, ...)

# After
await safe_background_task(
    lambda session: some_task(session, ...),
    "task_name_for_logging"
)
```

### 5. Database Monitoring Middleware

**File**: `surfsense_backend/app/middleware/db_monitoring.py`

Added middleware to monitor connection pool health:
- Logs pool statistics periodically
- Warns when pool utilization is high
- Tracks slow requests that might indicate connection issues

### 6. Health Check Endpoints

**File**: `surfsense_backend/app/routes/health_routes.py`

Added endpoints for monitoring:
- `/api/v1/health` - Basic health check
- `/api/v1/health/database` - Database connectivity check
- `/api/v1/health/pool` - Connection pool status
- `/api/v1/health/detailed` - Comprehensive health check
- `/api/v1/health/pool/force-cleanup` - Emergency pool cleanup

### 7. Enhanced Error Handling

**File**: `surfsense_backend/app/db.py`

Improved the `get_async_session()` function with better error handling:

```python
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    session = None
    try:
        session = async_session_maker()
        yield session
    except Exception as e:
        if session:
            await session.rollback()
            logger.error(f"Database session error, rolling back: {str(e)}")
        raise
    finally:
        if session:
            try:
                await session.close()
            except Exception as e:
                logger.error(f"Error closing database session: {str(e)}")
```

## Testing

**File**: `surfsense_backend/test_connection_pool.py`

Created comprehensive test suite to verify:
- Basic session management
- Concurrent session handling
- Error handling and cleanup
- Background task wrappers
- Pool monitoring
- Stress testing
- Leak detection

Run tests with:
```bash
cd surfsense_backend
python test_connection_pool.py
```

## Monitoring and Maintenance

### Pool Status Monitoring

Check connection pool status:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8000/api/v1/health/pool
```

### Log Analysis

Monitor logs for:
- Connection pool warnings
- Slow request alerts
- Session error messages

### Key Metrics to Watch

1. **Pool Utilization**: Should stay below 70% under normal load
2. **Checked Out Connections**: Should return to baseline after operations
3. **Request Duration**: Requests taking >5s may indicate connection issues

## Best Practices Going Forward

1. **Always use context managers** for database sessions
2. **Use `safe_background_task()`** for all background operations
3. **Monitor pool health** regularly via health endpoints
4. **Handle errors properly** to ensure session cleanup
5. **Avoid passing sessions** between different lifecycle contexts
6. **Use dedicated sessions** for long-running operations

## Emergency Procedures

If connection pool issues occur:

1. Check pool status: `GET /api/v1/health/pool`
2. Review recent logs for error patterns
3. If critical, use force cleanup: `POST /api/v1/health/pool/force-cleanup`
4. Restart application if necessary

## Files Modified

- `surfsense_backend/app/db.py` - Enhanced engine config and session management
- `surfsense_backend/app/utils/db_utils.py` - New session utilities
- `surfsense_backend/app/routes/chats_routes.py` - Fixed streaming operations
- `surfsense_backend/app/routes/documents_routes.py` - Updated background tasks
- `surfsense_backend/app/routes/search_source_connectors_routes.py` - Updated background tasks
- `surfsense_backend/app/routes/podcasts_routes.py` - Updated background tasks
- `surfsense_backend/app/middleware/db_monitoring.py` - New monitoring middleware
- `surfsense_backend/app/routes/health_routes.py` - New health endpoints
- `surfsense_backend/app/app.py` - Added middleware and routes
- `surfsense_backend/test_connection_pool.py` - Test suite
- `surfsense_backend/CONNECTION_POOL_FIXES.md` - This documentation
