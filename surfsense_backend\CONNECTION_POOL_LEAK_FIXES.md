# Connection Pool Leak Fixes

## Vấn đề

Hệ thống SurfSense gặp phải lỗi connection pool leak với thông báo:

```
The garbage collector is trying to clean up non-checked-in connection, which will be terminated. 
Please ensure that SQLAlchemy pooled connections are returned to the pool explicitly.
```

## Nguyên nhân chính

1. **Streaming operations không cleanup sessions đúng cách**: Trong các streaming operations dài hạn, sessions có thể không được đóng khi có exception hoặc client disconnect.

2. **Agent workflows giữ sessions quá lâu**: Các researcher agents và sub-agents giữ database sessions trong thời gian dài mà không có timeout mechanism.

3. **Thiếu monitoring và tracking**: Không có cách nào để track và monitor active sessions để detect leaks sớm.

## Giải pháp đã triển khai

### 1. Cải thiện Session Management trong Streaming Operations

**File**: `surfsense_backend/app/tasks/stream_connector_search_results.py`

- Thêm timeout cho streaming operations (30 phút)
- Cải thiện error handling và logging
- Đảm bảo cleanup trong finally block

```python
# Thêm timeout và better error handling
async with asyncio.timeout(STREAMING_TIMEOUT):
    async for chunk in researcher_graph.astream(...):
        yield chunk['yeild_value']
```

### 2. Tracked Session Management

**File**: `surfsense_backend/app/utils/session_cleanup.py`

Tạo hệ thống tracking sessions với:

- **SessionTracker**: Track tất cả active sessions
- **tracked_session()**: Context manager với automatic cleanup
- **Background cleanup task**: Tự động cleanup stale sessions

```python
async with tracked_session("streaming_search") as session:
    # Session được track và tự động cleanup
    result = await session.execute(query)
```

### 3. Connection Pool Monitoring

**File**: `surfsense_backend/app/middleware/connection_pool_monitor.py`

- **ConnectionPoolMonitorMiddleware**: Monitor pool status cho mỗi request
- **Leak detection**: Detect khi connections không được trả về sau request
- **Alerting**: Cảnh báo khi pool usage cao

### 4. Cập nhật Streaming Routes

**File**: `surfsense_backend/app/routes/chats_routes.py`

- Sử dụng `tracked_session()` thay vì manual session management
- Cải thiện error handling
- Tự động cleanup sessions

### 5. Enhanced Logging và Error Handling

**File**: `surfsense_backend/app/utils/connector_service.py`

- Thêm structured logging
- Better exception handling cho database operations
- Proper error categorization

## Cách sử dụng

### 1. Thêm Middleware (Optional)

Trong `main.py` hoặc app initialization:

```python
from app.middleware.connection_pool_monitor import ConnectionPoolMonitorMiddleware

app.add_middleware(ConnectionPoolMonitorMiddleware)
```

### 2. Start Background Cleanup Task

```python
from app.utils.session_cleanup import cleanup_background_task
import asyncio

# Start background task
asyncio.create_task(cleanup_background_task())
```

### 3. Sử dụng Tracked Sessions

Thay vì:
```python
async with async_session_maker() as session:
    # Manual session management
    pass
```

Sử dụng:
```python
from app.utils.session_cleanup import tracked_session

async with tracked_session("operation_name") as session:
    # Automatic tracking và cleanup
    pass
```

## Testing

Chạy test script để verify fixes:

```bash
cd surfsense_backend
python test_connection_pool_fix.py
```

Test script sẽ:
- Test tracked session management
- Test session tracker functionality
- Test connection pool monitoring
- Simulate streaming operations
- Test stale session cleanup

## Monitoring

### 1. Log Messages

Các log messages quan trọng:

- `"Created tracked session for streaming search"`: Session được tạo
- `"Session closed for streaming search"`: Session được đóng đúng cách
- `"Potential connection leak detected"`: Phát hiện leak
- `"High connection pool usage"`: Pool usage cao

### 2. Pool Status Monitoring

```python
from app.middleware.connection_pool_monitor import log_pool_status

# Log current pool status
log_pool_status("custom_context")
```

### 3. Session Tracker Status

```python
from app.utils.session_cleanup import session_tracker

# Get current status
status = await session_tracker.get_status()
print(f"Active sessions: {status['active_sessions']}")
```

## Best Practices

1. **Luôn sử dụng tracked_session()** cho operations mới
2. **Set timeout** cho long-running operations
3. **Monitor pool usage** thường xuyên
4. **Log errors** với proper context
5. **Test session cleanup** trong development

## Kết quả mong đợi

Sau khi áp dụng các fixes:

1. ✅ Không còn connection pool leak warnings
2. ✅ Sessions được cleanup tự động
3. ✅ Better monitoring và alerting
4. ✅ Improved error handling
5. ✅ Reduced memory usage

## Troubleshooting

### Nếu vẫn thấy leak warnings:

1. Check logs để xem sessions nào không được cleanup
2. Verify rằng tracked_session() được sử dụng
3. Check background cleanup task có đang chạy không
4. Monitor pool status để identify patterns

### High pool usage:

1. Increase pool size nếu cần thiết
2. Optimize database queries
3. Reduce session lifetime
4. Check for stale sessions

## Configuration

Có thể adjust các parameters:

```python
# Session cleanup intervals
CLEANUP_INTERVAL = 300  # 5 minutes
MAX_SESSION_AGE = 1800  # 30 minutes

# Pool monitoring
LOG_THRESHOLD = 0.8  # Log when 80% pool usage
LOG_EVERY_N_REQUESTS = 10  # Log every 10 requests
```
