#!/usr/bin/env python3
"""
Test script to verify connection pool fixes and detect potential leaks.
This script tests the improved session management and monitoring.
"""
import asyncio
import logging
import sys
import time
from typing import List

# Add the app directory to the path
sys.path.append('app')

from app.db import engine
from app.utils.session_cleanup import tracked_session, session_tracker, cleanup_background_task
from app.middleware.connection_pool_monitor import log_pool_status
from sqlalchemy import text

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_tracked_session_management():
    """Test the new tracked session management."""
    logger.info("Testing tracked session management...")
    
    # Test 1: Normal session usage
    async with tracked_session("test_normal") as session:
        result = await session.execute(text("SELECT 1"))
        assert result.scalar() == 1
    
    # Test 2: Session with error (should still cleanup)
    try:
        async with tracked_session("test_error") as session:
            await session.execute(text("SELECT 1"))
            raise Exception("Test error")
    except Exception:
        pass  # Expected
    
    # Test 3: Multiple concurrent sessions
    async def concurrent_session(session_id: int):
        async with tracked_session(f"test_concurrent_{session_id}") as session:
            await session.execute(text("SELECT 1"))
            await asyncio.sleep(0.1)  # Simulate some work
    
    tasks = [concurrent_session(i) for i in range(5)]
    await asyncio.gather(*tasks)
    
    logger.info("✓ Tracked session management test passed")


async def test_session_tracker():
    """Test the session tracker functionality."""
    logger.info("Testing session tracker...")
    
    # Get initial status
    initial_status = await session_tracker.get_status()
    logger.info(f"Initial tracker status: {initial_status}")
    
    # Create some tracked sessions
    sessions = []
    for i in range(3):
        async with tracked_session(f"tracker_test_{i}") as session:
            sessions.append(session)
            await session.execute(text("SELECT 1"))
    
    # Check status after sessions are closed
    final_status = await session_tracker.get_status()
    logger.info(f"Final tracker status: {final_status}")
    
    logger.info("✓ Session tracker test passed")


async def test_connection_pool_monitoring():
    """Test connection pool monitoring."""
    logger.info("Testing connection pool monitoring...")
    
    # Log initial pool status
    log_pool_status("test_start")
    
    # Create multiple sessions to stress test the pool
    async def stress_session(session_id: int):
        async with tracked_session(f"stress_test_{session_id}") as session:
            # Simulate database work
            for i in range(5):
                await session.execute(text("SELECT pg_sleep(0.01)"))
    
    # Run stress test
    tasks = [stress_session(i) for i in range(10)]
    await asyncio.gather(*tasks)
    
    # Log final pool status
    log_pool_status("test_end")
    
    logger.info("✓ Connection pool monitoring test passed")


async def test_stale_session_cleanup():
    """Test stale session detection and cleanup."""
    logger.info("Testing stale session cleanup...")
    
    # This test would require creating sessions that don't get properly closed
    # For now, just test the cleanup mechanism
    stale_sessions = await session_tracker.get_stale_sessions(max_age_seconds=1)
    logger.info(f"Found {len(stale_sessions)} stale sessions")
    
    cleanup_count = await session_tracker.force_cleanup_stale_sessions(max_age_seconds=1)
    logger.info(f"Cleaned up {cleanup_count} stale sessions")
    
    logger.info("✓ Stale session cleanup test passed")


async def test_streaming_simulation():
    """Simulate streaming operations to test session management."""
    logger.info("Testing streaming simulation...")
    
    async def simulate_streaming_operation(operation_id: int):
        """Simulate a streaming operation like the chat functionality."""
        context = f"streaming_simulation_{operation_id}"
        
        try:
            async with tracked_session(context) as session:
                # Simulate multiple database operations during streaming
                for i in range(10):
                    await session.execute(text("SELECT 1"))
                    await asyncio.sleep(0.01)  # Simulate streaming delay
                    
                    # Update session activity
                    session_id = id(session)
                    await session_tracker.update_activity(session_id)
                    
        except Exception as e:
            logger.error(f"Error in streaming simulation {operation_id}: {str(e)}")
    
    # Run multiple streaming simulations concurrently
    tasks = [simulate_streaming_operation(i) for i in range(5)]
    await asyncio.gather(*tasks)
    
    logger.info("✓ Streaming simulation test passed")


async def run_all_tests():
    """Run all connection pool tests."""
    logger.info("Starting connection pool fix tests...")
    
    try:
        await test_tracked_session_management()
        await test_session_tracker()
        await test_connection_pool_monitoring()
        await test_stale_session_cleanup()
        await test_streaming_simulation()
        
        logger.info("🎉 All connection pool tests passed!")
        
        # Final status report
        final_status = await session_tracker.get_status()
        logger.info(f"Final session tracker status: {final_status}")
        log_pool_status("all_tests_complete")
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}", exc_info=True)
        return False
    
    return True


async def run_background_monitoring():
    """Run background monitoring for a short period."""
    logger.info("Starting background monitoring test...")
    
    # Start background cleanup task
    cleanup_task = asyncio.create_task(cleanup_background_task())
    
    try:
        # Let it run for 10 seconds
        await asyncio.sleep(10)
    finally:
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass
    
    logger.info("Background monitoring test completed")


if __name__ == "__main__":
    async def main():
        success = await run_all_tests()
        
        if success:
            logger.info("Running background monitoring test...")
            await run_background_monitoring()
            
        return success
    
    # Run the tests
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
