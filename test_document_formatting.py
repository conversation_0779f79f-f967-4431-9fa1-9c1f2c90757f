#!/usr/bin/env python3
"""
Test script to verify document formatting and prioritization.

This script tests how documents are formatted and ordered when presented to the LLM.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'surfsense_backend'))

from app.agents.researcher.utils import format_documents_section

def create_test_documents():
    """Create test documents with different scores."""
    documents = [
        {
            "chunk_id": "doc_1",
            "content": "This is document 1 with high relevance score.",
            "score": 0.95,
            "document": {
                "id": "1",
                "title": "High Score Document",
                "document_type": "TEST",
                "metadata": {}
            }
        },
        {
            "chunk_id": "doc_2", 
            "content": "This is document 2 with medium relevance score.",
            "score": 0.75,
            "document": {
                "id": "2",
                "title": "Medium Score Document",
                "document_type": "TEST",
                "metadata": {}
            }
        },
        {
            "chunk_id": "doc_3",
            "content": "This is document 3 with low relevance score.",
            "score": 0.45,
            "document": {
                "id": "3", 
                "title": "Low Score Document",
                "document_type": "TEST",
                "metadata": {}
            }
        },
        {
            "chunk_id": "doc_4",
            "content": "This is document 4 with very low relevance score.",
            "score": 0.25,
            "document": {
                "id": "4",
                "title": "Very Low Score Document", 
                "document_type": "TEST",
                "metadata": {}
            }
        },
        {
            "chunk_id": "doc_5",
            "content": "This is document 5 with highest relevance score.",
            "score": 0.98,
            "document": {
                "id": "5",
                "title": "Highest Score Document",
                "document_type": "TEST", 
                "metadata": {}
            }
        }
    ]
    return documents

def test_document_formatting():
    """Test how documents are formatted and ordered."""
    print("=== Testing Document Formatting and Prioritization ===")
    
    # Create test data
    documents = create_test_documents()
    
    print(f"\nOriginal documents order (as they would come from reranking):")
    for i, doc in enumerate(documents):
        print(f"  {i+1}. {doc['document']['title']} (score: {doc['score']})")
    
    # Test document formatting
    formatted_section = format_documents_section(documents, "Test Source Material")
    
    print(f"\n=== FORMATTED OUTPUT ===")
    print(formatted_section)
    print("=== END FORMATTED OUTPUT ===")
    
    # Analyze the output
    lines = formatted_section.split('\n')
    priority_ranks = []
    scores = []
    
    for line in lines:
        if '<priority_rank>' in line:
            rank = line.strip().replace('<priority_rank>', '').replace('</priority_rank>', '')
            priority_ranks.append(int(rank))
        if '<relevance_score>' in line:
            score = line.strip().replace('<relevance_score>', '').replace('</relevance_score>', '')
            scores.append(float(score))
    
    print(f"\nAnalysis:")
    print(f"  Priority ranks found: {priority_ranks}")
    print(f"  Relevance scores found: {scores}")
    
    if priority_ranks:
        print(f"  Highest priority rank: {max(priority_ranks)} (should appear last)")
        print(f"  Lowest priority rank: {min(priority_ranks)} (should appear first)")
        
        # Check if highest priority appears last
        last_priority = priority_ranks[-1] if priority_ranks else 0
        highest_priority = max(priority_ranks) if priority_ranks else 0
        
        if last_priority == highest_priority:
            print("  ✓ Highest priority document appears last (good for LLM attention)")
        else:
            print("  ✗ Highest priority document does NOT appear last")
    
    if scores:
        print(f"  Highest score: {max(scores):.3f}")
        print(f"  Lowest score: {min(scores):.3f}")
        
        # Check if highest score appears last
        last_score = scores[-1] if scores else 0
        highest_score = max(scores) if scores else 0
        
        if abs(last_score - highest_score) < 0.001:  # Float comparison
            print("  ✓ Highest scoring document appears last (good for LLM attention)")
        else:
            print("  ✗ Highest scoring document does NOT appear last")
    
    print(f"\n🎯 Summary:")
    print(f"   Documents are now formatted with priority information")
    print(f"   Highest priority documents appear last to leverage LLM recency bias")
    print(f"   Each document includes priority_rank and relevance_score metadata")

if __name__ == "__main__":
    test_document_formatting()
