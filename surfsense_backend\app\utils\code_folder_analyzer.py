"""
Code Folder Analyzer Service

Analyzes code folders and generates comprehensive markdown documentation.
"""
import os
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import mimetypes
import chardet

logger = logging.getLogger(__name__)

@dataclass
class FileInfo:
    """Information about a single file."""
    path: str
    relative_path: str
    size: int
    extension: str
    mime_type: str
    is_text: bool
    content: Optional[str] = None
    lines_count: Optional[int] = None
    language: Optional[str] = None

@dataclass
class FolderAnalysis:
    """Complete analysis of a code folder."""
    root_path: str
    total_files: int
    total_size: int
    file_types: Dict[str, int]
    languages: Dict[str, int]
    folder_structure: Dict[str, any]
    files: List[FileInfo]
    readme_files: List[FileInfo]
    config_files: List[FileInfo]
    main_files: List[FileInfo]

class CodeFolderAnalyzer:
    """Analyzes code folders and generates documentation."""
    
    # Supported text file extensions
    TEXT_EXTENSIONS = {
        '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h', '.hpp',
        '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.r',
        '.sql', '.html', '.css', '.scss', '.sass', '.less', '.xml', '.json',
        '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.md', '.txt',
        '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd', '.dockerfile',
        '.gitignore', '.gitattributes', '.env', '.editorconfig', '.prettierrc'
    }
    
    # Language mapping
    LANGUAGE_MAP = {
        '.py': 'Python', '.js': 'JavaScript', '.ts': 'TypeScript',
        '.jsx': 'React JSX', '.tsx': 'React TSX', '.java': 'Java',
        '.cpp': 'C++', '.c': 'C', '.h': 'C/C++ Header', '.hpp': 'C++ Header',
        '.cs': 'C#', '.php': 'PHP', '.rb': 'Ruby', '.go': 'Go',
        '.rs': 'Rust', '.swift': 'Swift', '.kt': 'Kotlin', '.scala': 'Scala',
        '.r': 'R', '.sql': 'SQL', '.html': 'HTML', '.css': 'CSS',
        '.scss': 'SCSS', '.sass': 'Sass', '.less': 'Less', '.xml': 'XML',
        '.json': 'JSON', '.yaml': 'YAML', '.yml': 'YAML', '.toml': 'TOML',
        '.md': 'Markdown', '.sh': 'Shell Script', '.bash': 'Bash',
        '.dockerfile': 'Dockerfile'
    }
    
    # Files to ignore
    IGNORE_PATTERNS = {
        '__pycache__', '.git', '.svn', '.hg', 'node_modules', '.vscode',
        '.idea', 'dist', 'build', 'target', '.DS_Store', 'Thumbs.db',
        '*.pyc', '*.pyo', '*.pyd', '.pytest_cache', '.coverage',
        '.env.local', '.env.development', '.env.production'
    }
    
    def __init__(self, max_file_size: int = 1024 * 1024):  # 1MB default
        self.max_file_size = max_file_size
    
    async def analyze_folder(self, folder_path: str) -> FolderAnalysis:
        """Analyze a code folder and return comprehensive analysis."""
        folder_path = Path(folder_path).resolve()
        
        if not folder_path.exists():
            raise ValueError(f"Folder does not exist: {folder_path}")
        
        if not folder_path.is_dir():
            raise ValueError(f"Path is not a directory: {folder_path}")
        
        logger.info(f"Starting analysis of folder: {folder_path}")
        
        # Collect all files
        files = await self._collect_files(folder_path)
        
        # Analyze files
        analyzed_files = []
        for file_path in files:
            try:
                file_info = await self._analyze_file(file_path, folder_path)
                analyzed_files.append(file_info)
            except Exception as e:
                logger.warning(f"Failed to analyze file {file_path}: {e}")
        
        # Generate statistics
        total_size = sum(f.size for f in analyzed_files)
        file_types = {}
        languages = {}
        
        for file_info in analyzed_files:
            # Count file types
            ext = file_info.extension or 'no_extension'
            file_types[ext] = file_types.get(ext, 0) + 1
            
            # Count languages
            if file_info.language:
                languages[file_info.language] = languages.get(file_info.language, 0) + 1
        
        # Categorize special files
        readme_files = [f for f in analyzed_files if 'readme' in f.relative_path.lower()]
        config_files = [f for f in analyzed_files if self._is_config_file(f)]
        main_files = [f for f in analyzed_files if self._is_main_file(f)]
        
        # Generate folder structure
        folder_structure = await self._generate_folder_structure(folder_path)
        
        return FolderAnalysis(
            root_path=str(folder_path),
            total_files=len(analyzed_files),
            total_size=total_size,
            file_types=file_types,
            languages=languages,
            folder_structure=folder_structure,
            files=analyzed_files,
            readme_files=readme_files,
            config_files=config_files,
            main_files=main_files
        )
    
    async def _collect_files(self, folder_path: Path) -> List[Path]:
        """Collect all files in the folder, respecting ignore patterns."""
        files = []
        
        for root, dirs, filenames in os.walk(folder_path):
            # Filter out ignored directories
            dirs[:] = [d for d in dirs if not self._should_ignore(d)]
            
            for filename in filenames:
                if not self._should_ignore(filename):
                    file_path = Path(root) / filename
                    files.append(file_path)
        
        return files
    
    def _should_ignore(self, name: str) -> bool:
        """Check if a file or directory should be ignored."""
        return any(pattern in name for pattern in self.IGNORE_PATTERNS)
    
    async def _analyze_file(self, file_path: Path, root_path: Path) -> FileInfo:
        """Analyze a single file."""
        stat = file_path.stat()
        relative_path = str(file_path.relative_to(root_path))
        extension = file_path.suffix.lower()
        
        # Get MIME type
        mime_type, _ = mimetypes.guess_type(str(file_path))
        mime_type = mime_type or 'application/octet-stream'
        
        # Determine if it's a text file
        is_text = extension in self.TEXT_EXTENSIONS or mime_type.startswith('text/')
        
        # Get language
        language = self.LANGUAGE_MAP.get(extension)
        
        file_info = FileInfo(
            path=str(file_path),
            relative_path=relative_path,
            size=stat.st_size,
            extension=extension,
            mime_type=mime_type,
            is_text=is_text,
            language=language
        )
        
        # Read content for text files (if not too large)
        if is_text and stat.st_size <= self.max_file_size:
            try:
                content = await self._read_file_content(file_path)
                file_info.content = content
                file_info.lines_count = len(content.splitlines()) if content else 0
            except Exception as e:
                logger.warning(f"Failed to read content of {file_path}: {e}")
        
        return file_info
    
    async def _read_file_content(self, file_path: Path) -> str:
        """Read file content with encoding detection."""
        try:
            # Try UTF-8 first
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Detect encoding
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
            
            # Read with detected encoding
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                return f.read()
    
    async def _generate_folder_structure(self, folder_path: Path) -> Dict:
        """Generate a tree structure of the folder."""
        def build_tree(path: Path, max_depth: int = 3, current_depth: int = 0) -> Dict:
            if current_depth >= max_depth:
                return {"...": "truncated"}
            
            tree = {}
            try:
                for item in sorted(path.iterdir()):
                    if self._should_ignore(item.name):
                        continue
                    
                    if item.is_dir():
                        tree[f"{item.name}/"] = build_tree(item, max_depth, current_depth + 1)
                    else:
                        tree[item.name] = {"type": "file", "size": item.stat().st_size}
            except PermissionError:
                tree["<permission_denied>"] = True
            
            return tree
        
        return build_tree(folder_path)
    
    def _is_config_file(self, file_info: FileInfo) -> bool:
        """Check if a file is a configuration file."""
        config_patterns = [
            'config', 'settings', 'package.json', 'requirements.txt',
            'Cargo.toml', 'pom.xml', 'build.gradle', 'Makefile',
            'docker', '.env', '.gitignore', 'tsconfig', 'webpack'
        ]
        filename = file_info.relative_path.lower()
        return any(pattern in filename for pattern in config_patterns)
    
    def _is_main_file(self, file_info: FileInfo) -> bool:
        """Check if a file is a main/entry file."""
        main_patterns = [
            'main.', 'index.', 'app.', '__init__.py', 'server.',
            'client.', 'run.', 'start.', 'entry.'
        ]
        filename = os.path.basename(file_info.relative_path).lower()
        return any(filename.startswith(pattern) for pattern in main_patterns)
