# Shared SearchSpace và Chat Riêng Biệt - Hướng Dẫn

## Tổng Quan

Tính năng mới cho phép:
- **SearchSpace được chia sẻ** giữa nhiều users (shared data)
- **Chat riêng biệt** cho từng user (private chats)

Điều này có nghĩa là nhiều users có thể truy cập cùng một SearchSpace để tìm kiếm thông tin, nhưng mỗi user sẽ có chat history riêng của họ.

## Thay Đổi Database

### 1. Bảng `chats` 
- **Thêm cột `user_id`**: Mỗi chat thuộc về một user cụ thể
- **Foreign key**: `chats.user_id` → `user.id`

### 2. Bảng `searchspace_users` (mới)
- **Junction table** cho many-to-many relationship giữa SearchSpace và User
- **Columns**:
  - `search_space_id`: ID của SearchSpace
  - `user_id`: ID của User
  - `role`: <PERSON><PERSON> trò ('owner', 'admin', 'member', 'viewer')
  - `created_at`, `updated_at`: Timestamps

### 3. Migration
- Tự động migrate dữ liệu hiện tại
- Chats hiện tại sẽ thuộc về owner của SearchSpace
- Owners hiện tại sẽ được thêm vào `searchspace_users` với role 'owner'

## API Endpoints Mới

### 1. Chia Sẻ SearchSpace

```http
POST /api/v1/searchspaces/{search_space_id}/share
```

**Body:**
```json
{
  "user_email": "<EMAIL>",
  "role": "member"
}
```

**Response:**
```json
{
  "id": 1,
  "search_space_id": 123,
  "user_id": "uuid",
  "user_email": "<EMAIL>", 
  "role": "member",
  "created_at": "2025-01-08T12:00:00Z",
  "updated_at": "2025-01-08T12:00:00Z"
}
```

### 2. Xem Danh Sách Users Có Quyền Truy Cập

```http
GET /api/v1/searchspaces/{search_space_id}/shares
```

**Response:**
```json
[
  {
    "id": 1,
    "search_space_id": 123,
    "user_id": "uuid",
    "user_email": "<EMAIL>",
    "role": "member",
    "created_at": "2025-01-08T12:00:00Z",
    "updated_at": "2025-01-08T12:00:00Z"
  }
]
```

### 3. Xóa Quyền Truy Cập

```http
DELETE /api/v1/searchspaces/{search_space_id}/shares/{share_id}
```

### 4. Xem SearchSpaces Được Chia Sẻ

```http
GET /api/v1/searchspaces/shared
```

**Response:**
```json
[
  {
    "id": 123,
    "name": "Company Knowledge Base",
    "description": "Shared company documents",
    "role": "member",
    "shared_at": "2025-01-08T12:00:00Z",
    "owner_id": "owner-uuid"
  }
]
```

## API Endpoints Đã Cập Nhật

### 1. Lấy SearchSpaces

```http
GET /api/v1/searchspaces/?include_shared=true
```

- Mặc định bao gồm cả owned và shared SearchSpaces
- Set `include_shared=false` để chỉ lấy owned SearchSpaces

### 2. Lấy Chats

```http
GET /api/v1/chats/?search_space_id=123
```

- Chỉ trả về chats của user hiện tại
- Nếu có `search_space_id`, kiểm tra quyền truy cập SearchSpace trước

### 3. Tạo Chat

```http
POST /api/v1/chats/
```

- Tự động thêm `user_id` của user hiện tại
- Kiểm tra quyền truy cập SearchSpace (owned hoặc shared)

### 4. Lấy Chat Cụ Thể

```http
GET /api/v1/chats/{chat_id}
```

- Chỉ trả về chat nếu thuộc về user hiện tại

## Roles và Permissions

### Roles
- **owner**: Chủ sở hữu SearchSpace (có thể share, unshare, delete)
- **admin**: Quản trị viên (có thể share, unshare)
- **member**: Thành viên (có thể tạo chats, search)
- **viewer**: Chỉ xem (có thể search, không thể tạo chats)

### Permissions Matrix

| Action | Owner | Admin | Member | Viewer |
|--------|-------|-------|--------|--------|
| View SearchSpace | ✅ | ✅ | ✅ | ✅ |
| Search Documents | ✅ | ✅ | ✅ | ✅ |
| Create Chats | ✅ | ✅ | ✅ | ❌ |
| View Own Chats | ✅ | ✅ | ✅ | ✅ |
| Share SearchSpace | ✅ | ✅ | ❌ | ❌ |
| Remove Shares | ✅ | ✅ | ❌ | ❌ |
| Delete SearchSpace | ✅ | ❌ | ❌ | ❌ |
| Add Documents | ✅ | ✅ | ❌ | ❌ |

## Workflow Sử Dụng

### 1. Owner Chia Sẻ SearchSpace

```javascript
// 1. Owner shares SearchSpace with team member
const response = await fetch('/api/v1/searchspaces/123/share', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    user_email: '<EMAIL>',
    role: 'member'
  })
});
```

### 2. Shared User Truy Cập

```javascript
// 2. Shared user sees the SearchSpace in their list
const searchSpaces = await fetch('/api/v1/searchspaces/').then(r => r.json());
// Includes both owned and shared SearchSpaces

// 3. Shared user creates their own chat
const chat = await fetch('/api/v1/chats/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    search_space_id: 123,
    title: 'My Research Session',
    type: 'QNA',
    messages: []
  })
});
```

### 3. Riêng Biệt Chat Histories

```javascript
// Each user only sees their own chats
const myChats = await fetch('/api/v1/chats/?search_space_id=123').then(r => r.json());
// Only returns chats created by the current user
```

## Migration Script

Để áp dụng thay đổi:

```bash
# 1. Run migration
cd surfsense_backend
alembic upgrade head

# 2. Restart application
# The app will automatically handle the new schema
```

## Backward Compatibility

- ✅ Existing SearchSpaces vẫn hoạt động bình thường
- ✅ Existing Chats được migrate tự động
- ✅ API endpoints cũ vẫn hoạt động
- ✅ Không cần thay đổi frontend code hiện tại

## Security Considerations

1. **Chat Privacy**: Users chỉ có thể xem chats của họ
2. **SearchSpace Access**: Kiểm tra quyền truy cập cho mọi operation
3. **Share Permissions**: Chỉ owner/admin có thể share
4. **Data Isolation**: Mỗi user có chat history riêng biệt

## Testing

Sử dụng các test cases sau để verify tính năng:

1. **Share SearchSpace**: Owner share với user khác
2. **Access Control**: Shared user có thể search nhưng không thể share tiếp
3. **Chat Isolation**: User A không thể xem chats của User B
4. **Permission Levels**: Test các roles khác nhau
5. **Migration**: Verify dữ liệu cũ được migrate đúng
