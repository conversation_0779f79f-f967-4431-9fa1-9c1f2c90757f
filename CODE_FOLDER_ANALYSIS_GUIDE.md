# Code Folder Analysis - Hướng Dẫn Sử Dụng

## Tổng Quan

Tính năng **Code Folder Analysis** cho phép bạn phân tích toàn bộ cấu trúc và nội dung của một folder code, sau đó tạo ra một document markdown tổng quát và đầy đủ thông tin về dự án đó.

### ✨ Tính Năng Chính

- **Phân tích cấu trúc folder**: Hiển thị tree structure của dự án
- **Thống kê chi tiết**: Số lượng files, ngôn ngữ, kích thước
- **Phân tích code**: Tự động nhận diện main files, config files, documentation
- **Tạo documentation**: Sử dụng AI để tạo overview và phân tích chi tiết
- **Hỗ trợ nhiều ngôn ngữ**: Python, JavaScript, TypeScript, Java, C++, và nhiều hơn nữa
- **Upload archive**: Hỗ trợ upload file .zip, .tar.gz, .tar

## Cách Sử Dụng

### 1. Phân Tích Folder Local

```http
POST /api/v1/analyze-code-folder
```

**Request Body:**
```json
{
  "folder_path": "/path/to/your/project",
  "search_space_id": 123,
  "include_code_samples": true,
  "include_file_details": true,
  "document_title": "My Project Analysis"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully analyzed folder and created document",
  "document_id": 456,
  "analysis_summary": {
    "folder_path": "/path/to/your/project",
    "total_files": 45,
    "total_size": 1024000,
    "file_types": 8,
    "languages": ["Python", "JavaScript", "HTML"],
    "main_files": ["main.py", "index.js"],
    "config_files": ["package.json", "requirements.txt"],
    "readme_files": ["README.md"]
  }
}
```

### 2. Upload và Phân Tích Archive

```http
POST /api/v1/analyze-code-upload
```

**Form Data:**
- `file`: Archive file (.zip, .tar.gz, .tar)
- `search_space_id`: ID của SearchSpace
- `include_code_samples`: true/false
- `include_file_details`: true/false
- `document_title`: Tên document (optional)

### 3. Preview Phân Tích

```http
GET /api/v1/code-analysis-preview?folder_path=/path/to/project
```

Trả về thông tin preview mà không tạo document.

## Cấu Trúc Document Được Tạo

Document markdown được tạo sẽ bao gồm các phần:

### 1. Project Overview
- Tên và mô tả dự án
- Mục đích và chức năng chính
- Technology stack
- Kiến trúc tổng quan
- Tính năng chính
- Hướng dẫn bắt đầu

### 2. Project Structure
```
project-name/
├── src/
│   ├── main.py
│   ├── models.py
│   └── utils.py
├── tests/
│   ├── test_main.py
│   └── test_utils.py
├── config/
│   └── settings.py
├── README.md
├── requirements.txt
└── Dockerfile
```

### 3. File Analysis
- **Main Entry Points**: Các file chính của dự án
- **Documentation Files**: README, docs
- **Configuration Files**: package.json, requirements.txt, etc.

### 4. Code Samples
Ví dụ code cho từng ngôn ngữ được sử dụng:

```python
# Python example
from fastapi import FastAPI

app = FastAPI(title="Test API")

@app.get("/")
async def root():
    return {"message": "Hello World"}
```

### 5. Project Statistics

| Language | Files | Percentage |
|----------|-------|------------|
| Python | 15 | 45.5% |
| JavaScript | 10 | 30.3% |
| HTML | 5 | 15.2% |

### 6. Configuration Files
Chi tiết về các file cấu hình quan trọng.

## Ngôn Ngữ Được Hỗ Trợ

- **Backend**: Python, Java, C++, C, C#, PHP, Ruby, Go, Rust, Swift, Kotlin, Scala
- **Frontend**: JavaScript, TypeScript, React JSX/TSX, HTML, CSS, SCSS, Sass
- **Config**: JSON, YAML, TOML, XML, INI
- **Scripts**: Shell, Bash, PowerShell, Batch
- **Other**: SQL, R, Markdown, Dockerfile

## Tùy Chọn Cấu Hình

### include_code_samples
- `true`: Bao gồm code samples trong document
- `false`: Chỉ phân tích cấu trúc, không include code

### include_file_details
- `true`: Phân tích chi tiết từng file quan trọng
- `false`: Chỉ thống kê tổng quan

### document_title
- Tùy chỉnh tên document
- Mặc định: "Code Analysis: {folder_name}"

## Giới Hạn và Lưu Ý

### Giới Hạn Kích Thước
- **Max file size**: 1MB per file để đọc content
- **Archive upload**: Tùy thuộc vào cấu hình server
- **Total analysis**: Không giới hạn số lượng files

### Files Được Bỏ Qua
- `__pycache__`, `.git`, `.svn`, `node_modules`
- `.vscode`, `.idea`, `dist`, `build`, `target`
- Binary files, images (trừ khi cần thiết)

### Bảo Mật
- Chỉ phân tích folders mà user có quyền truy cập
- Không lưu trữ code content trên server
- Document được tạo thuộc về SearchSpace của user

## Frontend Integration

### React Component Example

```tsx
import CodeAnalysisComponent from './CodeAnalysisComponent';

function DocumentUpload({ searchSpaceId }) {
  const handleDocumentCreated = (documentId) => {
    console.log('New document created:', documentId);
    // Refresh document list or navigate to document
  };

  return (
    <CodeAnalysisComponent 
      searchSpaceId={searchSpaceId}
      onDocumentCreated={handleDocumentCreated}
    />
  );
}
```

### API Integration

```javascript
// Analyze folder
const analyzeFolder = async (folderPath, searchSpaceId) => {
  const response = await fetch('/api/v1/analyze-code-folder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      folder_path: folderPath,
      search_space_id: searchSpaceId,
      include_code_samples: true,
      include_file_details: true
    })
  });
  
  return await response.json();
};

// Upload and analyze
const analyzeUpload = async (file, searchSpaceId) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('search_space_id', searchSpaceId);
  
  const response = await fetch('/api/v1/analyze-code-upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  
  return await response.json();
};
```

## Use Cases

### 1. Project Documentation
- Tạo documentation tự động cho dự án mới
- Cập nhật documentation cho dự án hiện tại
- Onboarding team members mới

### 2. Code Review
- Hiểu cấu trúc dự án trước khi review
- Phân tích architecture và patterns
- Identify potential issues

### 3. Knowledge Management
- Lưu trữ thông tin về các dự án trong company
- Tạo knowledge base cho team
- Documentation cho legacy projects

### 4. Project Analysis
- So sánh các dự án khác nhau
- Phân tích technology stack
- Estimate effort cho maintenance

## Testing

Chạy test script để kiểm tra tính năng:

```bash
# Test với sample project
python test_code_analysis.py

# Test với folder cụ thể
python test_code_analysis.py /path/to/your/project
```

## Troubleshooting

### Lỗi Thường Gặp

1. **"Folder path does not exist"**
   - Kiểm tra đường dẫn folder có đúng không
   - Đảm bảo user có quyền truy cập folder

2. **"SearchSpace not found"**
   - Kiểm tra search_space_id có đúng không
   - Đảm bảo user có quyền truy cập SearchSpace

3. **"Analysis timeout"**
   - Folder quá lớn, thử với folder nhỏ hơn
   - Tắt include_code_samples và include_file_details

4. **"Upload failed"**
   - Kiểm tra file format (.zip, .tar.gz, .tar)
   - Kiểm tra kích thước file không quá giới hạn

### Performance Tips

- Sử dụng preview trước khi analyze folder lớn
- Tắt code samples cho projects lớn
- Upload archive thay vì analyze folder remote
