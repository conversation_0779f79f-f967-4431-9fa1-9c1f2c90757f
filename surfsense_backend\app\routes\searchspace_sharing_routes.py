"""
Routes for managing SearchSpace sharing functionality.
Allows users to share SearchSpaces with other users and manage access permissions.
"""
from typing import List
from fastapi import <PERSON><PERSON><PERSON>er, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import Integ<PERSON><PERSON><PERSON><PERSON>, OperationalError

from app.db import SearchSpace, SearchSpaceUser, User, get_async_session
from app.schemas import SearchSpaceUserCreate, SearchSpaceUserRead, SearchSpaceUserUpdate
from app.users import current_active_user
from app.utils.check_ownership import check_ownership

router = APIRouter()


@router.post("/searchspaces/{search_space_id}/share", response_model=SearchSpaceUserRead)
async def share_searchspace(
    search_space_id: int,
    share_data: SearchSpaceUserCreate,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """
    Share a SearchSpace with another user.
    Only the owner of the SearchSpace can share it.
    """
    try:
        # Check if current user owns the SearchSpace
        await check_ownership(session, SearchSpace, search_space_id, user)
        
        # Check if target user exists
        result = await session.execute(
            select(User).filter(User.email == share_data.user_email)
        )
        target_user = result.scalars().first()
        if not target_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if sharing already exists
        result = await session.execute(
            select(SearchSpaceUser).filter(
                SearchSpaceUser.search_space_id == search_space_id,
                SearchSpaceUser.user_id == target_user.id
            )
        )
        existing_share = result.scalars().first()
        if existing_share:
            raise HTTPException(status_code=400, detail="SearchSpace is already shared with this user")
        
        # Create the sharing relationship
        db_share = SearchSpaceUser(
            search_space_id=search_space_id,
            user_id=target_user.id,
            role=share_data.role
        )
        session.add(db_share)
        await session.commit()
        await session.refresh(db_share)
        
        return SearchSpaceUserRead(
            id=db_share.id,
            search_space_id=db_share.search_space_id,
            user_id=str(db_share.user_id),
            user_email=target_user.email,
            role=db_share.role,
            created_at=db_share.created_at,
            updated_at=db_share.updated_at
        )
        
    except HTTPException:
        raise
    except IntegrityError:
        await session.rollback()
        raise HTTPException(status_code=400, detail="Database constraint violation")
    except OperationalError:
        await session.rollback()
        raise HTTPException(status_code=503, detail="Database operation failed")
    except Exception:
        await session.rollback()
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.get("/searchspaces/{search_space_id}/shares", response_model=List[SearchSpaceUserRead])
async def list_searchspace_shares(
    search_space_id: int,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """
    List all users who have access to a SearchSpace.
    Only the owner can see the full list.
    """
    try:
        # Check if current user owns the SearchSpace
        await check_ownership(session, SearchSpace, search_space_id, user)
        
        # Get all shares for this SearchSpace
        result = await session.execute(
            select(SearchSpaceUser, User)
            .join(User, SearchSpaceUser.user_id == User.id)
            .filter(SearchSpaceUser.search_space_id == search_space_id)
        )
        shares = result.all()
        
        return [
            SearchSpaceUserRead(
                id=share.SearchSpaceUser.id,
                search_space_id=share.SearchSpaceUser.search_space_id,
                user_id=str(share.SearchSpaceUser.user_id),
                user_email=share.User.email,
                role=share.SearchSpaceUser.role,
                created_at=share.SearchSpaceUser.created_at,
                updated_at=share.SearchSpaceUser.updated_at
            )
            for share in shares
        ]
        
    except HTTPException:
        raise
    except Exception:
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.delete("/searchspaces/{search_space_id}/shares/{share_id}")
async def remove_searchspace_share(
    search_space_id: int,
    share_id: int,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """
    Remove a user's access to a SearchSpace.
    Only the owner can remove shares.
    """
    try:
        # Check if current user owns the SearchSpace
        await check_ownership(session, SearchSpace, search_space_id, user)
        
        # Find and delete the share
        result = await session.execute(
            select(SearchSpaceUser).filter(
                SearchSpaceUser.id == share_id,
                SearchSpaceUser.search_space_id == search_space_id
            )
        )
        share = result.scalars().first()
        if not share:
            raise HTTPException(status_code=404, detail="Share not found")
        
        await session.delete(share)
        await session.commit()
        
        return {"message": "Share removed successfully"}
        
    except HTTPException:
        raise
    except Exception:
        await session.rollback()
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.get("/searchspaces/shared", response_model=List[dict])
async def list_shared_searchspaces(
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """
    List all SearchSpaces that have been shared with the current user.
    """
    try:
        result = await session.execute(
            select(SearchSpace, SearchSpaceUser)
            .join(SearchSpaceUser, SearchSpace.id == SearchSpaceUser.search_space_id)
            .filter(SearchSpaceUser.user_id == user.id)
        )
        shared_spaces = result.all()
        
        return [
            {
                "id": space.SearchSpace.id,
                "name": space.SearchSpace.name,
                "description": space.SearchSpace.description,
                "role": space.SearchSpaceUser.role,
                "shared_at": space.SearchSpaceUser.created_at,
                "owner_id": str(space.SearchSpace.user_id)
            }
            for space in shared_spaces
        ]
        
    except Exception:
        raise HTTPException(status_code=500, detail="An unexpected error occurred")
