"""Add shared SearchSpace support and user-specific chats

Revision ID: add_shared_searchspace_support
Revises: [previous_revision_id]
Create Date: 2025-01-08 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_shared_searchspace_support'
down_revision = None  # Replace with actual previous revision
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add shared SearchSpace support and user-specific chats."""
    
    # 1. Add user_id to chats table to make chats user-specific
    op.add_column('chats', sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True))
    
    # 2. Create SearchSpaceUser junction table for many-to-many relationship
    op.create_table('searchspace_users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('search_space_id', sa.Integer(), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('role', sa.String(50), nullable=False, default='member'),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['search_space_id'], ['searchspaces.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('search_space_id', 'user_id', name='unique_searchspace_user')
    )
    
    # 3. Create indexes for better performance
    op.create_index('idx_searchspace_users_search_space_id', 'searchspace_users', ['search_space_id'])
    op.create_index('idx_searchspace_users_user_id', 'searchspace_users', ['user_id'])
    op.create_index('idx_chats_user_id', 'chats', ['user_id'])
    
    # 4. Migrate existing data: Set chat user_id to the owner of the search space
    op.execute("""
        UPDATE chats 
        SET user_id = searchspaces.user_id 
        FROM searchspaces 
        WHERE chats.search_space_id = searchspaces.id
    """)
    
    # 5. Migrate existing SearchSpaces: Add owners to SearchSpaceUser table
    op.execute("""
        INSERT INTO searchspace_users (search_space_id, user_id, role, created_at, updated_at)
        SELECT id, user_id, 'owner', created_at, updated_at
        FROM searchspaces
    """)
    
    # 6. Make user_id in chats NOT NULL after migration
    op.alter_column('chats', 'user_id', nullable=False)
    
    # 7. Add foreign key constraint for chats.user_id
    op.create_foreign_key('fk_chats_user_id', 'chats', 'user', ['user_id'], ['id'], ondelete='CASCADE')


def downgrade() -> None:
    """Remove shared SearchSpace support."""
    
    # Remove foreign key constraint
    op.drop_constraint('fk_chats_user_id', 'chats', type_='foreignkey')
    
    # Remove indexes
    op.drop_index('idx_chats_user_id', 'chats')
    op.drop_index('idx_searchspace_users_user_id', 'searchspace_users')
    op.drop_index('idx_searchspace_users_search_space_id', 'searchspace_users')
    
    # Drop SearchSpaceUser table
    op.drop_table('searchspace_users')
    
    # Remove user_id column from chats
    op.drop_column('chats', 'user_id')
