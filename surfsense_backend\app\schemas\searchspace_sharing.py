"""
Schemas for SearchSpace sharing functionality.
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr


class SearchSpaceUserBase(BaseModel):
    """Base schema for SearchSpace user associations."""
    role: str = "member"  # 'owner', 'admin', 'member', 'viewer'


class SearchSpaceUserCreate(SearchSpaceUserBase):
    """Schema for creating a new SearchSpace share."""
    user_email: EmailStr


class SearchSpaceUserUpdate(SearchSpaceUserBase):
    """Schema for updating a SearchSpace share."""
    role: Optional[str] = None


class SearchSpaceUserRead(SearchSpaceUserBase):
    """Schema for reading SearchSpace user associations."""
    id: int
    search_space_id: int
    user_id: str
    user_email: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SharedSearchSpaceRead(BaseModel):
    """Schema for reading shared SearchSpaces."""
    id: int
    name: str
    description: Optional[str] = None
    role: str
    shared_at: datetime
    owner_id: str

    class Config:
        from_attributes = True
