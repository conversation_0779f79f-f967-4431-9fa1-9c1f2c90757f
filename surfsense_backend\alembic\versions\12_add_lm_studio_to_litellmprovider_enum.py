"""Add LM_STUDIO to LiteLLMProvider enum

Revision ID: 12
Revises: 11
"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "12"
down_revision: Union[str, None] = "11"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema - add LM_STUDIO to LiteLLMProvider enum."""
    
    # Add LM_STUDIO to the existing enum
    op.execute("ALTER TYPE litellmprovider ADD VALUE 'LM_STUDIO'")


def downgrade() -> None:
    """Downgrade schema - remove LM_STUDIO from LiteLLMProvider enum."""
    
    # Note: PostgreSQL doesn't support removing enum values directly
    # This would require recreating the enum type, which is complex
    # For now, we'll leave the enum value in place
    # In production, you might want to implement a more complex downgrade
    pass
