"""
Database monitoring middleware to track connection pool health.
"""
import logging
import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.db import engine

logger = logging.getLogger(__name__)


class DatabaseMonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware to monitor database connection pool health and log warnings
    when the pool is getting full or connections are being held too long.
    """
    
    def __init__(self, app, log_pool_stats: bool = False):
        super().__init__(app)
        self.log_pool_stats = log_pool_stats
        self.request_count = 0
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Log pool stats periodically if enabled
        if self.log_pool_stats:
            self.request_count += 1
            if self.request_count % 100 == 0:  # Log every 100 requests
                await self._log_pool_stats()
        
        try:
            response = await call_next(request)
            
            # Log slow requests that might indicate connection issues
            duration = time.time() - start_time
            if duration > 5.0:  # Log requests taking more than 5 seconds
                logger.warning(
                    f"Slow request detected: {request.method} {request.url.path} "
                    f"took {duration:.2f}s - possible connection pool issue"
                )
                await self._log_pool_stats()
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"Request failed after {duration:.2f}s: {request.method} {request.url.path} "
                f"Error: {str(e)}"
            )
            await self._log_pool_stats()
            raise
    
    async def _log_pool_stats(self):
        """Log current connection pool statistics."""
        try:
            pool = engine.pool
            logger.info(
                f"Connection Pool Stats - "
                f"Size: {pool.size()}, "
                f"Checked out: {pool.checkedout()}, "
                f"Overflow: {pool.overflow()}, "
                f"Checked in: {pool.checkedin()}"
            )
            
            # Warn if pool is getting full
            if pool.checkedout() > (pool.size() * 0.8):
                logger.warning(
                    f"Connection pool is {(pool.checkedout() / pool.size()) * 100:.1f}% full. "
                    f"Consider investigating for connection leaks."
                )
                
        except Exception as e:
            logger.error(f"Error getting pool stats: {str(e)}")


def log_pool_status():
    """Utility function to manually log pool status."""
    try:
        pool = engine.pool
        logger.info(
            f"Manual Pool Check - "
            f"Size: {pool.size()}, "
            f"Checked out: {pool.checkedout()}, "
            f"Overflow: {pool.overflow()}, "
            f"Checked in: {pool.checkedin()}"
        )
        return {
            "size": pool.size(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "checked_in": pool.checkedin()
        }
    except Exception as e:
        logger.error(f"Error getting pool stats: {str(e)}")
        return None
