/**
 * Code Analysis Component Example
 * 
 * This is an example React component that demonstrates how to integrate
 * the code folder analysis feature into the SurfSense frontend.
 */

import React, { useState } from 'react';
import { Button, Input, Card, Upload, Switch, message, Spin } from 'antd';
import { UploadOutlined, FolderOpenOutlined, FileTextOutlined } from '@ant-design/icons';

interface CodeAnalysisProps {
  searchSpaceId: number;
  onDocumentCreated?: (documentId: number) => void;
}

interface AnalysisResult {
  success: boolean;
  message: string;
  document_id?: number;
  analysis_summary?: {
    folder_path?: string;
    original_filename?: string;
    total_files: number;
    total_size: number;
    file_types: number;
    languages: string[];
    main_files: string[];
    config_files: string[];
    readme_files: string[];
  };
}

const CodeAnalysisComponent: React.FC<CodeAnalysisProps> = ({
  searchSpaceId,
  onDocumentCreated
}) => {
  const [folderPath, setFolderPath] = useState('');
  const [documentTitle, setDocumentTitle] = useState('');
  const [includeCodeSamples, setIncludeCodeSamples] = useState(true);
  const [includeFileDetails, setIncludeFileDetails] = useState(true);
  const [loading, setLoading] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);
  const [analysisMode, setAnalysisMode] = useState<'folder' | 'upload'>('folder');

  const handleFolderAnalysis = async () => {
    if (!folderPath.trim()) {
      message.error('Please enter a folder path');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/analyze-code-folder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}` // Adjust based on your auth
        },
        body: JSON.stringify({
          folder_path: folderPath,
          search_space_id: searchSpaceId,
          include_code_samples: includeCodeSamples,
          include_file_details: includeFileDetails,
          document_title: documentTitle || undefined
        })
      });

      const result: AnalysisResult = await response.json();

      if (result.success) {
        message.success(result.message);
        if (result.document_id && onDocumentCreated) {
          onDocumentCreated(result.document_id);
        }
        // Reset form
        setFolderPath('');
        setDocumentTitle('');
        setPreviewData(null);
      } else {
        message.error(result.message || 'Analysis failed');
      }
    } catch (error) {
      message.error('Failed to analyze folder: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePreview = async () => {
    if (!folderPath.trim()) {
      message.error('Please enter a folder path');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/v1/code-analysis-preview?folder_path=${encodeURIComponent(folderPath)}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const preview = await response.json();
        setPreviewData(preview);
        message.success('Preview generated successfully');
      } else {
        message.error('Failed to generate preview');
      }
    } catch (error) {
      message.error('Failed to preview: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('search_space_id', searchSpaceId.toString());
    formData.append('include_code_samples', includeCodeSamples.toString());
    formData.append('include_file_details', includeFileDetails.toString());
    if (documentTitle) {
      formData.append('document_title', documentTitle);
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/analyze-code-upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      const result: AnalysisResult = await response.json();

      if (result.success) {
        message.success(result.message);
        if (result.document_id && onDocumentCreated) {
          onDocumentCreated(result.document_id);
        }
        setDocumentTitle('');
      } else {
        message.error(result.message || 'Upload analysis failed');
      }
    } catch (error) {
      message.error('Failed to analyze uploaded file: ' + error.message);
    } finally {
      setLoading(false);
    }

    return false; // Prevent default upload behavior
  };

  const formatFileSize = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  return (
    <div className="code-analysis-component">
      <Card title="Code Folder Analysis" className="mb-4">
        <div className="mb-4">
          <Button.Group>
            <Button 
              type={analysisMode === 'folder' ? 'primary' : 'default'}
              icon={<FolderOpenOutlined />}
              onClick={() => setAnalysisMode('folder')}
            >
              Analyze Folder Path
            </Button>
            <Button 
              type={analysisMode === 'upload' ? 'primary' : 'default'}
              icon={<UploadOutlined />}
              onClick={() => setAnalysisMode('upload')}
            >
              Upload Archive
            </Button>
          </Button.Group>
        </div>

        {analysisMode === 'folder' ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Folder Path</label>
              <Input
                placeholder="Enter absolute path to code folder (e.g., /path/to/project)"
                value={folderPath}
                onChange={(e) => setFolderPath(e.target.value)}
                prefix={<FolderOpenOutlined />}
              />
            </div>

            <div className="flex gap-4">
              <Button onClick={handlePreview} disabled={loading}>
                Preview Analysis
              </Button>
              <Button type="primary" onClick={handleFolderAnalysis} loading={loading}>
                Analyze & Create Document
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Upload Code Archive</label>
              <Upload
                beforeUpload={handleFileUpload}
                accept=".zip,.tar.gz,.tar"
                showUploadList={false}
                disabled={loading}
              >
                <Button icon={<UploadOutlined />} loading={loading}>
                  Select Archive File (.zip, .tar.gz, .tar)
                </Button>
              </Upload>
            </div>
          </div>
        )}

        <div className="mt-4 space-y-3">
          <div>
            <label className="block text-sm font-medium mb-2">Document Title (Optional)</label>
            <Input
              placeholder="Custom title for the generated document"
              value={documentTitle}
              onChange={(e) => setDocumentTitle(e.target.value)}
              prefix={<FileTextOutlined />}
            />
          </div>

          <div className="flex gap-6">
            <div className="flex items-center gap-2">
              <Switch
                checked={includeCodeSamples}
                onChange={setIncludeCodeSamples}
                size="small"
              />
              <span className="text-sm">Include Code Samples</span>
            </div>
            <div className="flex items-center gap-2">
              <Switch
                checked={includeFileDetails}
                onChange={setIncludeFileDetails}
                size="small"
              />
              <span className="text-sm">Include File Details</span>
            </div>
          </div>
        </div>

        {previewData && (
          <Card title="Analysis Preview" className="mt-4" size="small">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Total Files:</strong> {previewData.total_files}
              </div>
              <div>
                <strong>Total Size:</strong> {formatFileSize(previewData.total_size)}
              </div>
              <div>
                <strong>File Types:</strong> {previewData.file_types ? Object.keys(previewData.file_types).length : 0}
              </div>
              <div>
                <strong>Languages:</strong> {previewData.languages ? Object.keys(previewData.languages).join(', ') : 'None'}
              </div>
            </div>
            
            {previewData.main_files && previewData.main_files.length > 0 && (
              <div className="mt-3">
                <strong>Main Files:</strong>
                <ul className="list-disc list-inside text-sm mt-1">
                  {previewData.main_files.slice(0, 5).map((file: string, index: number) => (
                    <li key={index}>{file}</li>
                  ))}
                </ul>
              </div>
            )}
          </Card>
        )}
      </Card>
    </div>
  );
};

export default CodeAnalysisComponent;
