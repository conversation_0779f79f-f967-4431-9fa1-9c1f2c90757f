#!/usr/bin/env python3
"""
Test script to verify connection pool improvements and detect potential leaks.
"""
import asyncio
import logging
import sys
import time
from typing import List

# Add the app directory to the path
sys.path.append('app')

from app.db import async_session_maker, engine
from app.utils.db_utils import get_db_session, safe_background_task
from app.middleware.db_monitoring import log_pool_status
from sqlalchemy import text

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_basic_session_management():
    """Test basic session creation and cleanup."""
    logger.info("Testing basic session management...")
    
    # Test 1: Normal session usage
    async with get_db_session() as session:
        result = await session.execute(text("SELECT 1"))
        assert result.scalar() == 1
    
    logger.info("✓ Basic session management test passed")


async def test_multiple_concurrent_sessions():
    """Test multiple concurrent sessions to check for leaks."""
    logger.info("Testing multiple concurrent sessions...")
    
    async def create_session_task(task_id: int):
        async with get_db_session() as session:
            await session.execute(text("SELECT pg_sleep(0.1)"))  # Small delay
            return f"Task {task_id} completed"
    
    # Create 20 concurrent tasks
    tasks = [create_session_task(i) for i in range(20)]
    results = await asyncio.gather(*tasks)
    
    assert len(results) == 20
    logger.info("✓ Multiple concurrent sessions test passed")


async def test_session_error_handling():
    """Test that sessions are properly cleaned up even when errors occur."""
    logger.info("Testing session error handling...")
    
    try:
        async with get_db_session() as session:
            # This should cause an error
            await session.execute(text("SELECT * FROM non_existent_table"))
    except Exception:
        pass  # Expected error
    
    logger.info("✓ Session error handling test passed")


async def test_background_task_wrapper():
    """Test the safe_background_task wrapper."""
    logger.info("Testing background task wrapper...")
    
    async def sample_task(session):
        result = await session.execute(text("SELECT 42"))
        return result.scalar()
    
    result = await safe_background_task(sample_task, "test_task")
    assert result == 42
    
    logger.info("✓ Background task wrapper test passed")


async def test_pool_monitoring():
    """Test pool monitoring functionality."""
    logger.info("Testing pool monitoring...")
    
    # Get initial pool stats
    initial_stats = log_pool_status()
    assert initial_stats is not None
    assert "size" in initial_stats
    assert "checked_out" in initial_stats
    
    logger.info(f"Pool stats: {initial_stats}")
    logger.info("✓ Pool monitoring test passed")


async def stress_test_sessions(num_sessions: int = 50, concurrent: bool = True):
    """Stress test session management."""
    logger.info(f"Stress testing with {num_sessions} sessions (concurrent={concurrent})...")
    
    async def stress_task(task_id: int):
        async with get_db_session() as session:
            # Simulate some database work
            await session.execute(text("SELECT pg_sleep(0.01)"))
            result = await session.execute(text("SELECT :task_id"), {"task_id": task_id})
            return result.scalar()
    
    start_time = time.time()
    
    if concurrent:
        tasks = [stress_task(i) for i in range(num_sessions)]
        results = await asyncio.gather(*tasks)
    else:
        results = []
        for i in range(num_sessions):
            result = await stress_task(i)
            results.append(result)
    
    duration = time.time() - start_time
    
    assert len(results) == num_sessions
    logger.info(f"✓ Stress test completed in {duration:.2f}s")
    
    # Check pool stats after stress test
    final_stats = log_pool_status()
    logger.info(f"Pool stats after stress test: {final_stats}")


async def test_session_leak_detection():
    """Test for potential session leaks by monitoring pool stats."""
    logger.info("Testing session leak detection...")
    
    # Get baseline stats
    baseline_stats = log_pool_status()
    baseline_checked_out = baseline_stats["checked_out"]
    
    # Create and use multiple sessions
    for i in range(10):
        async with get_db_session() as session:
            await session.execute(text("SELECT 1"))
    
    # Wait a moment for cleanup
    await asyncio.sleep(0.1)
    
    # Check stats again
    final_stats = log_pool_status()
    final_checked_out = final_stats["checked_out"]
    
    # Should be back to baseline (or very close)
    if final_checked_out > baseline_checked_out + 1:
        logger.warning(f"Potential session leak detected: baseline={baseline_checked_out}, final={final_checked_out}")
    else:
        logger.info("✓ No session leaks detected")


async def main():
    """Run all tests."""
    logger.info("Starting connection pool tests...")
    
    try:
        # Basic tests
        await test_basic_session_management()
        await test_multiple_concurrent_sessions()
        await test_session_error_handling()
        await test_background_task_wrapper()
        await test_pool_monitoring()
        
        # Stress tests
        await stress_test_sessions(50, concurrent=True)
        await stress_test_sessions(20, concurrent=False)
        
        # Leak detection
        await test_session_leak_detection()
        
        logger.info("🎉 All tests passed!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        raise
    
    finally:
        # Clean up
        await engine.dispose()
        logger.info("Engine disposed")


if __name__ == "__main__":
    asyncio.run(main())
