---
title: Installation
description: Current ways to use SurfSense
full: true
---

# Installing SurfSense

There are two ways to install SurfSense, but both require the repository to be cloned first. Clone [SurfSense](https://github.com/MODSetter/SurfSense) and then:

## Docker Installation 

This method provides a containerized environment with all dependencies pre-configured. Less Customization.

[Learn more about Docker installation](/docs/docker-installation)

## Manual Installation (Preferred)

For users who prefer more control over the installation process or need to customize their setup, we also provide manual installation instructions.

[Learn more about Manual installation](/docs/manual-installation)