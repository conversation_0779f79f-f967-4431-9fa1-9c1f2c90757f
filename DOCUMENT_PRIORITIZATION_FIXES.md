# Document Prioritization Fixes

## Problem Description

User reported that SurfSense was prioritizing documents at lower positions (e.g., document 3) over documents at higher positions (e.g., document 1) in search results. The issue was: "thông tin tôi cần nó nằm ở các tài liệu đầu nhưng model lại chọn các thông tin của các tài liệu cuối" (the information I need is in the first documents but the model chooses information from the last documents).

## Root Cause Analysis

The issue was identified in two main areas:

1. **Token Optimization Priority**: The `optimize_documents_for_token_limit` function was not properly prioritizing documents by relevance score when applying token limits.

2. **LLM Attention Bias**: Large Language Models tend to pay more attention to information that appears later in the context (recency bias), but documents were being presented in the wrong order.

## Fixes Implemented

### 1. Enhanced Document Sorting in Token Optimization

**File**: `surfsense_backend/app/agents/researcher/utils.py`

**Changes**:
- Modified `optimize_documents_for_token_limit()` to sort documents by score in descending order before applying token limits
- Added logging to show score ranges and document prioritization
- Ensured that when token limits are applied, the highest-scoring documents are selected first

**Key Code Changes**:
```python
# Sort documents by score in descending order to prioritize high-relevance documents
sorted_documents = sorted(documents, key=lambda x: x.get("score", 0), reverse=True)

print(f"Document prioritization: Sorted {len(sorted_documents)} documents by score")
if sorted_documents:
    highest_score = sorted_documents[0].get("score", 0)
    lowest_score = sorted_documents[-1].get("score", 0)
    print(f"Score range: {highest_score:.4f} (highest) to {lowest_score:.4f} (lowest)")
```

### 2. Improved Document Formatting with Priority Information

**File**: `surfsense_backend/app/agents/researcher/utils.py`

**Changes**:
- Enhanced `format_document_for_citation()` to include priority rank and relevance score in document metadata
- Modified `format_documents_section()` to reverse document order so highest priority documents appear last
- Added explanatory text to help LLM understand document ordering

**Key Code Changes**:
```python
def format_documents_section(documents: List[Dict[str, Any]], section_title: str = "Source material") -> str:
    """
    Format multiple documents into a complete documents section.
    
    Documents are presented in reverse order (highest priority last) to leverage
    LLM's recency bias - the tendency to pay more attention to information that
    appears later in the context.
    """
    # Reverse the order so highest priority documents appear last
    reversed_docs = list(reversed(documents))
    
    # Format each document with priority information
    formatted_docs = []
    for i, doc in enumerate(reversed_docs):
        priority_rank = len(documents) - i  # 1 = highest priority
        formatted_doc = format_document_for_citation(doc, priority_rank)
        formatted_docs.append(formatted_doc)

    return f"""{section_title} (ordered by relevance, highest priority documents appear last):
    <documents>
    {chr(10).join(formatted_docs)}
    </documents>"""
```

### 3. Enhanced System Prompts

**Files**: 
- `surfsense_backend/app/agents/researcher/sub_section_writer/prompts.py`
- `surfsense_backend/app/agents/researcher/qna_agent/prompts.py`

**Changes**:
- Added explicit instructions for LLM to pay attention to priority_rank and relevance_score values
- Emphasized prioritizing information from higher-ranked documents
- Added guidance for handling conflicting information from documents with different priorities

**Key Prompt Changes**:
```
2. PRIORITY ATTENTION: Documents are ordered by relevance - pay special attention to documents with higher priority_rank values and relevance_score values, as these are most relevant to the user's query.
3. Extract relevant information that addresses the user's query, prioritizing information from higher-ranked documents.
...
10. If documents contain conflicting information, acknowledge this and present both perspectives with appropriate citations, giving more weight to higher-priority documents.
...
23. CRITICAL: When multiple documents contain relevant information, prioritize information from documents with higher priority_rank and relevance_score values.
```

### 4. Updated Binary Search Documentation

**File**: `surfsense_backend/app/agents/researcher/utils.py`

**Changes**:
- Added clear documentation to `find_optimal_documents_with_binary_search()` explaining that it assumes documents are pre-sorted by priority
- Clarified that the function selects documents from the beginning of the list (highest priority first)

## How the Fix Works

1. **Document Retrieval**: Documents are retrieved and ranked by the hybrid search system (vector + full-text search with RRF scoring)

2. **Reranking**: Documents are reranked using the reranker service and sorted by score in descending order

3. **Token Optimization**: When applying token limits, documents are now explicitly sorted by score to ensure highest-scoring documents are prioritized

4. **Formatting**: Documents are formatted with priority information and presented in reverse order so the most important documents appear last (leveraging LLM recency bias)

5. **LLM Processing**: The LLM receives clear instructions to prioritize information from documents with higher priority_rank and relevance_score values

## Expected Behavior After Fix

- Documents with higher relevance scores will be prioritized during token optimization
- The most relevant documents will appear last in the context (closest to the question)
- LLM will receive explicit guidance to focus on higher-priority documents
- Information from the most relevant documents should be used preferentially in responses

## Testing

Created test scripts:
- `test_document_prioritization.py`: Tests token optimization prioritization
- `test_document_formatting.py`: Tests document formatting and ordering

## Impact

This fix addresses the core issue where lower-ranked documents were being prioritized over higher-ranked documents, ensuring that the most relevant information is properly emphasized for the LLM.
