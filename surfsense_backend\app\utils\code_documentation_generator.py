"""
Code Documentation Generator

Generates comprehensive markdown documentation from code folder analysis.
"""
import json
import logging
from typing import Dict, List, Optional
from datetime import datetime

from app.utils.code_folder_analyzer import FolderAnalysis, FileInfo
from app.utils.llm_service import get_llm_response

logger = logging.getLogger(__name__)

class CodeDocumentationGenerator:
    """Generates markdown documentation from code analysis."""
    
    def __init__(self):
        self.max_content_length = 50000  # Max content to send to LLM
    
    async def generate_documentation(
        self, 
        analysis: FolderAnalysis,
        user_id: str,
        include_code_samples: bool = True,
        include_file_details: bool = True
    ) -> str:
        """Generate comprehensive markdown documentation."""
        
        logger.info(f"Generating documentation for {analysis.root_path}")
        
        # Generate different sections
        sections = []
        
        # 1. Overview section
        overview = await self._generate_overview(analysis, user_id)
        sections.append(overview)
        
        # 2. Project structure
        structure = self._generate_structure_section(analysis)
        sections.append(structure)
        
        # 3. File analysis
        if include_file_details:
            file_analysis = await self._generate_file_analysis(analysis, user_id)
            sections.append(file_analysis)
        
        # 4. Code samples
        if include_code_samples:
            code_samples = self._generate_code_samples(analysis)
            sections.append(code_samples)
        
        # 5. Statistics
        stats = self._generate_statistics(analysis)
        sections.append(stats)
        
        # 6. Configuration files
        config_section = self._generate_config_section(analysis)
        sections.append(config_section)
        
        # Combine all sections
        documentation = "\n\n".join(sections)
        
        return documentation
    
    async def _generate_overview(self, analysis: FolderAnalysis, user_id: str) -> str:
        """Generate project overview using LLM."""
        
        # Prepare context for LLM
        context = self._prepare_llm_context(analysis)
        
        prompt = f"""
Analyze this code project and provide a comprehensive overview in markdown format.

Project Information:
- Path: {analysis.root_path}
- Total Files: {analysis.total_files}
- Languages: {', '.join(analysis.languages.keys())}
- Main Files: {[f.relative_path for f in analysis.main_files[:5]]}

Context:
{context}

Please provide:
1. Project title and brief description
2. Main purpose and functionality
3. Technology stack used
4. Architecture overview
5. Key features identified
6. Getting started information (if applicable)

Format as markdown with appropriate headers. Be comprehensive but concise.
"""
        
        try:
            overview = await get_llm_response(prompt, user_id, model_type="strategic")
            return f"# Project Overview\n\n{overview}"
        except Exception as e:
            logger.error(f"Failed to generate LLM overview: {e}")
            return self._generate_basic_overview(analysis)
    
    def _generate_basic_overview(self, analysis: FolderAnalysis) -> str:
        """Generate basic overview without LLM."""
        project_name = analysis.root_path.split('/')[-1] or analysis.root_path.split('\\')[-1]
        
        overview = f"""# {project_name}

## Project Information

- **Location**: `{analysis.root_path}`
- **Total Files**: {analysis.total_files}
- **Total Size**: {self._format_size(analysis.total_size)}
- **Primary Languages**: {', '.join(list(analysis.languages.keys())[:5])}

## Quick Stats

- **File Types**: {len(analysis.file_types)} different types
- **Configuration Files**: {len(analysis.config_files)}
- **Documentation Files**: {len(analysis.readme_files)}
- **Main Entry Points**: {len(analysis.main_files)}

## Technology Stack

{self._generate_tech_stack(analysis)}
"""
        return overview
    
    def _generate_structure_section(self, analysis: FolderAnalysis) -> str:
        """Generate project structure section."""
        
        structure_text = "## Project Structure\n\n```\n"
        structure_text += self._format_folder_tree(analysis.folder_structure, analysis.root_path.split('/')[-1])
        structure_text += "\n```\n"
        
        # Add structure explanation
        structure_text += "\n### Directory Overview\n\n"
        
        # Analyze folder structure
        folders = self._extract_folders_from_structure(analysis.folder_structure)
        for folder in folders[:10]:  # Limit to top 10 folders
            structure_text += f"- **{folder}**: "
            if 'src' in folder.lower() or 'source' in folder.lower():
                structure_text += "Source code directory\n"
            elif 'test' in folder.lower():
                structure_text += "Test files directory\n"
            elif 'doc' in folder.lower():
                structure_text += "Documentation directory\n"
            elif 'config' in folder.lower():
                structure_text += "Configuration files\n"
            elif 'build' in folder.lower() or 'dist' in folder.lower():
                structure_text += "Build output directory\n"
            else:
                structure_text += "Project directory\n"
        
        return structure_text
    
    async def _generate_file_analysis(self, analysis: FolderAnalysis, user_id: str) -> str:
        """Generate detailed file analysis."""
        
        section = "## File Analysis\n\n"
        
        # Key files analysis
        if analysis.main_files:
            section += "### Main Entry Points\n\n"
            for file_info in analysis.main_files[:5]:
                section += f"#### {file_info.relative_path}\n"
                section += f"- **Language**: {file_info.language or 'Unknown'}\n"
                section += f"- **Size**: {self._format_size(file_info.size)}\n"
                if file_info.lines_count:
                    section += f"- **Lines**: {file_info.lines_count}\n"
                
                # Add code preview if available
                if file_info.content and len(file_info.content) < 2000:
                    section += f"\n```{self._get_language_code(file_info.language)}\n"
                    section += file_info.content[:1000]
                    if len(file_info.content) > 1000:
                        section += "\n... (truncated)"
                    section += "\n```\n"
                section += "\n"
        
        # README files
        if analysis.readme_files:
            section += "### Documentation Files\n\n"
            for readme in analysis.readme_files:
                section += f"- **{readme.relative_path}** ({self._format_size(readme.size)})\n"
        
        return section
    
    def _generate_code_samples(self, analysis: FolderAnalysis) -> str:
        """Generate code samples section."""
        
        section = "## Code Samples\n\n"
        
        # Group files by language
        by_language = {}
        for file_info in analysis.files:
            if file_info.language and file_info.content and file_info.is_text:
                if file_info.language not in by_language:
                    by_language[file_info.language] = []
                by_language[file_info.language].append(file_info)
        
        # Show samples for each language
        for language, files in list(by_language.items())[:5]:  # Limit to 5 languages
            section += f"### {language}\n\n"
            
            # Find a good representative file
            sample_file = None
            for file_info in files:
                if len(file_info.content) > 100 and len(file_info.content) < 3000:
                    sample_file = file_info
                    break
            
            if not sample_file and files:
                sample_file = files[0]
            
            if sample_file:
                section += f"**File**: `{sample_file.relative_path}`\n\n"
                section += f"```{self._get_language_code(language)}\n"
                content = sample_file.content[:2000]  # Limit content
                section += content
                if len(sample_file.content) > 2000:
                    section += "\n... (truncated)"
                section += "\n```\n\n"
        
        return section
    
    def _generate_statistics(self, analysis: FolderAnalysis) -> str:
        """Generate statistics section."""
        
        section = "## Project Statistics\n\n"
        
        # File type distribution
        section += "### File Types\n\n"
        section += "| Extension | Count | Percentage |\n"
        section += "|-----------|-------|------------|\n"
        
        total_files = analysis.total_files
        for ext, count in sorted(analysis.file_types.items(), key=lambda x: x[1], reverse=True)[:10]:
            percentage = (count / total_files) * 100
            section += f"| {ext or 'no extension'} | {count} | {percentage:.1f}% |\n"
        
        # Language distribution
        if analysis.languages:
            section += "\n### Languages\n\n"
            section += "| Language | Files | Percentage |\n"
            section += "|----------|-------|------------|\n"
            
            total_lang_files = sum(analysis.languages.values())
            for lang, count in sorted(analysis.languages.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_lang_files) * 100
                section += f"| {lang} | {count} | {percentage:.1f}% |\n"
        
        # Size statistics
        section += f"\n### Size Information\n\n"
        section += f"- **Total Size**: {self._format_size(analysis.total_size)}\n"
        section += f"- **Average File Size**: {self._format_size(analysis.total_size // analysis.total_files if analysis.total_files > 0 else 0)}\n"
        
        # Calculate largest files
        largest_files = sorted([f for f in analysis.files if f.size > 0], key=lambda x: x.size, reverse=True)[:5]
        if largest_files:
            section += f"\n**Largest Files**:\n"
            for file_info in largest_files:
                section += f"- `{file_info.relative_path}` ({self._format_size(file_info.size)})\n"
        
        return section
    
    def _generate_config_section(self, analysis: FolderAnalysis) -> str:
        """Generate configuration files section."""
        
        if not analysis.config_files:
            return ""
        
        section = "## Configuration Files\n\n"
        
        for config_file in analysis.config_files:
            section += f"### {config_file.relative_path}\n\n"
            section += f"- **Type**: {config_file.language or 'Configuration'}\n"
            section += f"- **Size**: {self._format_size(config_file.size)}\n"
            
            # Show content for small config files
            if config_file.content and len(config_file.content) < 1500:
                section += f"\n```{self._get_language_code(config_file.language)}\n"
                section += config_file.content
                section += "\n```\n"
            section += "\n"
        
        return section
    
    def _prepare_llm_context(self, analysis: FolderAnalysis) -> str:
        """Prepare context for LLM analysis."""
        context_parts = []
        
        # Add README content if available
        for readme in analysis.readme_files:
            if readme.content:
                context_parts.append(f"README ({readme.relative_path}):\n{readme.content[:2000]}")
        
        # Add main file content
        for main_file in analysis.main_files[:3]:
            if main_file.content:
                context_parts.append(f"Main file ({main_file.relative_path}):\n{main_file.content[:1500]}")
        
        # Add package.json or similar config files
        for config in analysis.config_files[:2]:
            if config.content and 'package.json' in config.relative_path.lower():
                context_parts.append(f"Config ({config.relative_path}):\n{config.content[:1000]}")
        
        context = "\n\n---\n\n".join(context_parts)
        
        # Limit total context length
        if len(context) > self.max_content_length:
            context = context[:self.max_content_length] + "\n... (truncated)"
        
        return context
    
    def _format_folder_tree(self, structure: Dict, root_name: str, prefix: str = "") -> str:
        """Format folder structure as a tree."""
        if not structure:
            return ""
        
        lines = []
        if not prefix:  # Root level
            lines.append(f"{root_name}/")
        
        items = list(structure.items())
        for i, (name, content) in enumerate(items):
            is_last = i == len(items) - 1
            current_prefix = "└── " if is_last else "├── "
            lines.append(f"{prefix}{current_prefix}{name}")
            
            if isinstance(content, dict) and name.endswith('/'):
                next_prefix = prefix + ("    " if is_last else "│   ")
                subtree = self._format_folder_tree(content, "", next_prefix)
                if subtree:
                    lines.append(subtree)
        
        return "\n".join(lines)
    
    def _extract_folders_from_structure(self, structure: Dict, path: str = "") -> List[str]:
        """Extract folder names from structure."""
        folders = []
        for name, content in structure.items():
            if name.endswith('/'):
                folder_path = f"{path}/{name[:-1]}" if path else name[:-1]
                folders.append(folder_path)
                if isinstance(content, dict):
                    folders.extend(self._extract_folders_from_structure(content, folder_path))
        return folders
    
    def _generate_tech_stack(self, analysis: FolderAnalysis) -> str:
        """Generate technology stack information."""
        tech_stack = []
        
        # Analyze based on languages and config files
        if 'Python' in analysis.languages:
            tech_stack.append("- **Python**: Backend/scripting")
        if 'JavaScript' in analysis.languages:
            tech_stack.append("- **JavaScript**: Frontend/Node.js")
        if 'TypeScript' in analysis.languages:
            tech_stack.append("- **TypeScript**: Type-safe JavaScript")
        if 'React JSX' in analysis.languages or 'React TSX' in analysis.languages:
            tech_stack.append("- **React**: Frontend framework")
        
        # Check for specific frameworks/tools
        config_files_text = " ".join([f.relative_path.lower() for f in analysis.config_files])
        if 'package.json' in config_files_text:
            tech_stack.append("- **Node.js**: JavaScript runtime")
        if 'requirements.txt' in config_files_text or 'pyproject.toml' in config_files_text:
            tech_stack.append("- **Python Package Management**: pip/poetry")
        if 'dockerfile' in config_files_text:
            tech_stack.append("- **Docker**: Containerization")
        
        return "\n".join(tech_stack) if tech_stack else "- Technology stack not clearly identified"
    
    def _format_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024
        return f"{size_bytes:.1f} TB"
    
    def _get_language_code(self, language: Optional[str]) -> str:
        """Get language code for syntax highlighting."""
        if not language:
            return ""
        
        language_codes = {
            'Python': 'python',
            'JavaScript': 'javascript',
            'TypeScript': 'typescript',
            'React JSX': 'jsx',
            'React TSX': 'tsx',
            'Java': 'java',
            'C++': 'cpp',
            'C': 'c',
            'C#': 'csharp',
            'PHP': 'php',
            'Ruby': 'ruby',
            'Go': 'go',
            'Rust': 'rust',
            'Swift': 'swift',
            'Kotlin': 'kotlin',
            'HTML': 'html',
            'CSS': 'css',
            'JSON': 'json',
            'YAML': 'yaml',
            'SQL': 'sql',
            'Shell Script': 'bash',
            'Dockerfile': 'dockerfile'
        }
        
        return language_codes.get(language, language.lower())
