#!/usr/bin/env python3
"""
Test script for Code Folder Analysis feature.

This script tests the code analysis functionality by analyzing a sample folder.
"""

import asyncio
import sys
import os
import tempfile
from pathlib import Path

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'surfsense_backend'))

from app.utils.code_folder_analyzer import CodeFolderAnalyzer
from app.utils.code_documentation_generator import CodeDocumentationGenerator

async def create_sample_project():
    """Create a sample project for testing."""
    temp_dir = tempfile.mkdtemp(prefix="test_project_")
    project_path = Path(temp_dir)
    
    # Create project structure
    (project_path / "src").mkdir()
    (project_path / "tests").mkdir()
    (project_path / "docs").mkdir()
    (project_path / "config").mkdir()
    
    # Create sample files
    files_to_create = {
        "README.md": """# Test Project

This is a sample project for testing the code analysis feature.

## Features
- Python backend
- React frontend
- Docker support

## Getting Started
1. Install dependencies
2. Run the application
""",
        "package.json": """{
  "name": "test-project",
  "version": "1.0.0",
  "description": "A test project",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.0",
    "react": "^18.0.0"
  }
}""",
        "requirements.txt": """fastapi==0.104.1
uvicorn==0.24.0
sqlalchemy==2.0.23
pydantic==2.5.0
""",
        "Dockerfile": """FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
""",
        "src/main.py": """#!/usr/bin/env python3
\"\"\"
Main application entry point.
\"\"\"

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Test API", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
""",
        "src/models.py": """\"\"\"
Database models for the application.
\"\"\"

from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"

class Post(Base):
    __tablename__ = "posts"
    
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False)
    content = Column(String(1000), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
""",
        "src/utils.py": """\"\"\"
Utility functions for the application.
\"\"\"

import hashlib
import secrets
from typing import Optional

def generate_hash(data: str) -> str:
    \"\"\"Generate SHA256 hash of the input data.\"\"\"
    return hashlib.sha256(data.encode()).hexdigest()

def generate_token(length: int = 32) -> str:
    \"\"\"Generate a secure random token.\"\"\"
    return secrets.token_urlsafe(length)

def validate_email(email: str) -> bool:
    \"\"\"Basic email validation.\"\"\"
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None
""",
        "tests/test_main.py": """\"\"\"
Tests for the main application.
\"\"\"

import pytest
from fastapi.testclient import TestClient
from src.main import app

client = TestClient(app)

def test_root():
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Hello World"}

def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}
""",
        "tests/test_utils.py": """\"\"\"
Tests for utility functions.
\"\"\"

import pytest
from src.utils import generate_hash, generate_token, validate_email

def test_generate_hash():
    data = "test_data"
    hash1 = generate_hash(data)
    hash2 = generate_hash(data)
    assert hash1 == hash2
    assert len(hash1) == 64  # SHA256 produces 64 character hex string

def test_generate_token():
    token1 = generate_token()
    token2 = generate_token()
    assert token1 != token2
    assert len(token1) > 0

def test_validate_email():
    assert validate_email("<EMAIL>") == True
    assert validate_email("invalid-email") == False
    assert validate_email("test@") == False
""",
        "config/settings.py": """\"\"\"
Application settings and configuration.
\"\"\"

import os
from typing import Optional

class Settings:
    # Database
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./test.db")
    
    # API
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    API_PORT: int = int(os.getenv("API_PORT", "8000"))
    
    # Security
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Features
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    ENABLE_CORS: bool = True

settings = Settings()
""",
        ".gitignore": """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Build
dist/
build/
*.egg-info/
""",
        "docker-compose.yml": """version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/testdb
    depends_on:
      - db
  
  db:
    image: postgres:13
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=testdb
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
"""
    }
    
    # Write all files
    for file_path, content in files_to_create.items():
        full_path = project_path / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        full_path.write_text(content)
    
    print(f"Created sample project at: {project_path}")
    return str(project_path)

async def test_code_analysis():
    """Test the complete code analysis workflow."""
    print("=== Testing Code Folder Analysis ===")
    
    # Create sample project
    project_path = await create_sample_project()
    
    try:
        # Initialize analyzer
        analyzer = CodeFolderAnalyzer()
        print(f"Analyzing project: {project_path}")
        
        # Analyze the folder
        analysis = await analyzer.analyze_folder(project_path)
        
        print(f"\n=== Analysis Results ===")
        print(f"Total files: {analysis.total_files}")
        print(f"Total size: {analysis.total_size} bytes")
        print(f"File types: {list(analysis.file_types.keys())}")
        print(f"Languages: {list(analysis.languages.keys())}")
        print(f"Main files: {[f.relative_path for f in analysis.main_files]}")
        print(f"Config files: {[f.relative_path for f in analysis.config_files]}")
        print(f"README files: {[f.relative_path for f in analysis.readme_files]}")
        
        # Test documentation generation
        print(f"\n=== Generating Documentation ===")
        doc_generator = CodeDocumentationGenerator()
        
        # Generate documentation (without LLM for testing)
        documentation = await doc_generator.generate_documentation(
            analysis=analysis,
            user_id="test_user",
            include_code_samples=True,
            include_file_details=True
        )
        
        print(f"Generated documentation length: {len(documentation)} characters")
        print(f"\n=== Documentation Preview ===")
        print(documentation[:1000] + "..." if len(documentation) > 1000 else documentation)
        
        # Save documentation to file for inspection
        doc_file = Path(project_path) / "GENERATED_DOCUMENTATION.md"
        doc_file.write_text(documentation)
        print(f"\nFull documentation saved to: {doc_file}")
        
        print(f"\n✅ Code analysis test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Code analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        import shutil
        try:
            shutil.rmtree(project_path)
            print(f"Cleaned up test project: {project_path}")
        except Exception as e:
            print(f"Failed to clean up: {e}")

async def test_specific_folder():
    """Test analysis on a specific folder (if provided as command line argument)."""
    if len(sys.argv) > 1:
        folder_path = sys.argv[1]
        print(f"=== Testing Analysis on Specific Folder: {folder_path} ===")
        
        try:
            analyzer = CodeFolderAnalyzer()
            analysis = await analyzer.analyze_folder(folder_path)
            
            print(f"Total files: {analysis.total_files}")
            print(f"Languages: {list(analysis.languages.keys())}")
            print(f"File types: {dict(list(analysis.file_types.items())[:10])}")
            
            # Generate preview documentation
            doc_generator = CodeDocumentationGenerator()
            documentation = await doc_generator.generate_documentation(
                analysis=analysis,
                user_id="test_user",
                include_code_samples=False,  # Skip code samples for large projects
                include_file_details=False
            )
            
            print(f"\nGenerated documentation preview:")
            print(documentation[:2000] + "..." if len(documentation) > 2000 else documentation)
            
        except Exception as e:
            print(f"Failed to analyze folder: {e}")

if __name__ == "__main__":
    async def main():
        # Test with sample project
        success = await test_code_analysis()
        
        # Test with specific folder if provided
        await test_specific_folder()
        
        return success
    
    # Run the tests
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
