"""
Code Analysis Routes

API endpoints for analyzing code folders and generating documentation.
"""
import os
import tempfile
import shutil
from pathlib import Path
from typing import Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel

from app.db import get_async_session, User, SearchSpace, Document, DocumentType
from app.users import current_active_user
from app.utils.code_folder_analyzer import CodeFolderAnalyzer
from app.utils.code_documentation_generator import CodeDocumentationGenerator
from app.utils.session_cleanup import tracked_session

router = APIRouter()

class CodeAnalysisRequest(BaseModel):
    """Request model for code analysis."""
    folder_path: str
    search_space_id: int
    include_code_samples: bool = True
    include_file_details: bool = True
    document_title: Optional[str] = None

class CodeAnalysisResponse(BaseModel):
    """Response model for code analysis."""
    success: bool
    message: str
    document_id: Optional[int] = None
    analysis_summary: Optional[dict] = None

@router.post("/analyze-code-folder", response_model=CodeAnalysisResponse)
async def analyze_code_folder(
    request: CodeAnalysisRequest,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """
    Analyze a code folder and create a comprehensive documentation.
    
    This endpoint:
    1. Analyzes the specified folder path
    2. Generates comprehensive markdown documentation
    3. Creates a document in the specified SearchSpace
    """
    try:
        # Validate folder path
        folder_path = Path(request.folder_path).resolve()
        if not folder_path.exists():
            raise HTTPException(status_code=400, detail="Folder path does not exist")
        
        if not folder_path.is_dir():
            raise HTTPException(status_code=400, detail="Path is not a directory")
        
        # Check if user has access to the SearchSpace
        result = await session.execute(
            select(SearchSpace).filter(
                SearchSpace.id == request.search_space_id,
                SearchSpace.user_id == user.id
            )
        )
        search_space = result.scalars().first()
        if not search_space:
            raise HTTPException(status_code=404, detail="SearchSpace not found or access denied")
        
        # Initialize analyzers
        analyzer = CodeFolderAnalyzer()
        doc_generator = CodeDocumentationGenerator()
        
        # Analyze the folder
        analysis = await analyzer.analyze_folder(str(folder_path))
        
        # Generate documentation
        documentation = await doc_generator.generate_documentation(
            analysis=analysis,
            user_id=str(user.id),
            include_code_samples=request.include_code_samples,
            include_file_details=request.include_file_details
        )
        
        # Create document title
        if request.document_title:
            title = request.document_title
        else:
            folder_name = folder_path.name
            title = f"Code Analysis: {folder_name}"
        
        # Create document in database
        document = Document(
            title=title,
            content=documentation,
            document_type=DocumentType.CODE_ANALYSIS,
            search_space_id=request.search_space_id,
            metadata={
                "folder_path": str(folder_path),
                "total_files": analysis.total_files,
                "total_size": analysis.total_size,
                "languages": list(analysis.languages.keys()),
                "analysis_date": datetime.utcnow().isoformat()
            }
        )
        
        session.add(document)
        await session.commit()
        await session.refresh(document)
        
        # Prepare analysis summary
        analysis_summary = {
            "folder_path": str(folder_path),
            "total_files": analysis.total_files,
            "total_size": analysis.total_size,
            "file_types": len(analysis.file_types),
            "languages": list(analysis.languages.keys()),
            "main_files": [f.relative_path for f in analysis.main_files],
            "config_files": [f.relative_path for f in analysis.config_files],
            "readme_files": [f.relative_path for f in analysis.readme_files]
        }
        
        return CodeAnalysisResponse(
            success=True,
            message=f"Successfully analyzed folder and created document: {title}",
            document_id=document.id,
            analysis_summary=analysis_summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze code folder: {str(e)}"
        )

@router.post("/analyze-code-upload", response_model=CodeAnalysisResponse)
async def analyze_code_upload(
    search_space_id: int = Form(...),
    include_code_samples: bool = Form(True),
    include_file_details: bool = Form(True),
    document_title: Optional[str] = Form(None),
    file: UploadFile = File(...),
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """
    Analyze uploaded code archive (zip file) and create documentation.
    
    This endpoint:
    1. Accepts a zip file upload
    2. Extracts it to a temporary directory
    3. Analyzes the code structure
    4. Generates comprehensive documentation
    5. Creates a document in the specified SearchSpace
    """
    temp_dir = None
    try:
        # Validate file type
        if not file.filename.endswith(('.zip', '.tar.gz', '.tar')):
            raise HTTPException(
                status_code=400, 
                detail="Only zip, tar.gz, and tar files are supported"
            )
        
        # Check if user has access to the SearchSpace
        result = await session.execute(
            select(SearchSpace).filter(
                SearchSpace.id == search_space_id,
                SearchSpace.user_id == user.id
            )
        )
        search_space = result.scalars().first()
        if not search_space:
            raise HTTPException(status_code=404, detail="SearchSpace not found or access denied")
        
        # Create temporary directory
        temp_dir = tempfile.mkdtemp(prefix="code_analysis_")
        
        # Save uploaded file
        file_path = Path(temp_dir) / file.filename
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Extract archive
        extract_dir = Path(temp_dir) / "extracted"
        extract_dir.mkdir()
        
        if file.filename.endswith('.zip'):
            import zipfile
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
        elif file.filename.endswith(('.tar.gz', '.tar')):
            import tarfile
            with tarfile.open(file_path, 'r:*') as tar_ref:
                tar_ref.extractall(extract_dir)
        
        # Find the main folder (handle single root folder case)
        extracted_items = list(extract_dir.iterdir())
        if len(extracted_items) == 1 and extracted_items[0].is_dir():
            analysis_folder = extracted_items[0]
        else:
            analysis_folder = extract_dir
        
        # Initialize analyzers
        analyzer = CodeFolderAnalyzer()
        doc_generator = CodeDocumentationGenerator()
        
        # Analyze the folder
        analysis = await analyzer.analyze_folder(str(analysis_folder))
        
        # Generate documentation
        documentation = await doc_generator.generate_documentation(
            analysis=analysis,
            user_id=str(user.id),
            include_code_samples=include_code_samples,
            include_file_details=include_file_details
        )
        
        # Create document title
        if document_title:
            title = document_title
        else:
            base_name = file.filename.rsplit('.', 1)[0]  # Remove extension
            title = f"Code Analysis: {base_name}"
        
        # Create document in database
        document = Document(
            title=title,
            content=documentation,
            document_type=DocumentType.CODE_ANALYSIS,
            search_space_id=search_space_id,
            metadata={
                "original_filename": file.filename,
                "total_files": analysis.total_files,
                "total_size": analysis.total_size,
                "languages": list(analysis.languages.keys()),
                "analysis_date": datetime.utcnow().isoformat()
            }
        )
        
        session.add(document)
        await session.commit()
        await session.refresh(document)
        
        # Prepare analysis summary
        analysis_summary = {
            "original_filename": file.filename,
            "total_files": analysis.total_files,
            "total_size": analysis.total_size,
            "file_types": len(analysis.file_types),
            "languages": list(analysis.languages.keys()),
            "main_files": [f.relative_path for f in analysis.main_files],
            "config_files": [f.relative_path for f in analysis.config_files],
            "readme_files": [f.relative_path for f in analysis.readme_files]
        }
        
        return CodeAnalysisResponse(
            success=True,
            message=f"Successfully analyzed uploaded code and created document: {title}",
            document_id=document.id,
            analysis_summary=analysis_summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze uploaded code: {str(e)}"
        )
    finally:
        # Clean up temporary directory
        if temp_dir and Path(temp_dir).exists():
            shutil.rmtree(temp_dir, ignore_errors=True)

@router.get("/code-analysis-preview")
async def preview_code_analysis(
    folder_path: str,
    user: User = Depends(current_active_user)
):
    """
    Preview code analysis without creating a document.
    Returns basic statistics about the folder.
    """
    try:
        # Validate folder path
        folder_path = Path(folder_path).resolve()
        if not folder_path.exists():
            raise HTTPException(status_code=400, detail="Folder path does not exist")
        
        if not folder_path.is_dir():
            raise HTTPException(status_code=400, detail="Path is not a directory")
        
        # Initialize analyzer
        analyzer = CodeFolderAnalyzer()
        
        # Analyze the folder (lightweight analysis)
        analysis = await analyzer.analyze_folder(str(folder_path))
        
        # Return preview information
        return {
            "folder_path": str(folder_path),
            "total_files": analysis.total_files,
            "total_size": analysis.total_size,
            "file_types": dict(list(analysis.file_types.items())[:10]),  # Top 10
            "languages": analysis.languages,
            "main_files": [f.relative_path for f in analysis.main_files[:5]],
            "config_files": [f.relative_path for f in analysis.config_files[:5]],
            "readme_files": [f.relative_path for f in analysis.readme_files],
            "estimated_doc_size": len(str(analysis.folder_structure)) + sum(
                len(f.content or "") for f in analysis.files[:20] if f.content
            )
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to preview code analysis: {str(e)}"
        )
